import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { DataModule } from '@modules/data/data.module';
import { DatabaseModule } from '@database/database.module';
import { ServicesModule } from '@shared/services/services.module';
import { AgentModule } from '@modules/agent/agent.module';
import { StrategyModule } from '@modules/strategy/strategy.module';
import { AuthModule } from '@modules/auth/auth.module';
import { UserModule } from '@modules/user/user.module';
import { ConfigModule } from '@config';
import { SubscriptionModule } from '@modules/subscription/subscription.module';
import { AffiliateModule } from '@modules/affiliate/affiliate.module';
import { MarketingUserModule } from '@modules/marketing/user/marketing-user.module';
import { MarketplaceModule } from '@modules/marketplace/marketplace.module';
import { IntegrationModule } from '@modules/integration/integration.module';
import { HelperModule } from '@common/helpers/helper.module';
import { EmployeeModule } from '@modules/employee/employee.module';
import { KnowledgeFilesModule } from '@modules/data/knowledge-files/knowledge-files.module';
import { CommonModule as GlobalCommonModule, RequestLoggerMiddleware } from '@/common';
import { CommonModule } from '@modules/common/common.module';
import { SystemConfigurationModule } from '@modules/system-configuration';
import { BlogModule } from '@modules/blog/blog.module';
import { RPointModule } from '@modules/r-point/r-point.module';
import { GoogleModule } from '@modules/google/google.module';
import { BusinessModule } from '@modules/business/business.module';
import { ToolsModule } from './modules/tools/tools.module';
import { ToolsBuildInModule } from './modules/tools-build-in/tools-build-in.module';
import { QueueModule } from '@shared/queue/queue.module';
import { ChatModule } from './modules/chat/chat.module';
import { TestModule } from './modules/test/test.module';
import { RuleContractModule } from './modules/rule-contract/rule-contract.module';

@Module({
  imports: [
    ConfigModule,
    DatabaseModule,
    ServicesModule,
    HelperModule,
    GlobalCommonModule,
    CommonModule,
    QueueModule,
    DataModule,
    AgentModule,
    StrategyModule,
    UserModule,
    AuthModule,
    AffiliateModule,
    MarketingUserModule,
    MarketplaceModule,
    SubscriptionModule,
    IntegrationModule,
    EmployeeModule,
    KnowledgeFilesModule,
    EmployeeModule,
    SystemConfigurationModule,
    BlogModule,
    RPointModule,
    GoogleModule,
    BusinessModule,
    ToolsModule,
    ToolsBuildInModule,
    ChatModule,
    TestModule,
    RuleContractModule,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestLoggerMiddleware).forRoutes('*');
  }
}
