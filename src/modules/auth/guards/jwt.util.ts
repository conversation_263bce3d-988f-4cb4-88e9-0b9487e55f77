import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@/common';

// Define TokenType enum
export enum TokenType {
  ACCESS = 'ACCESS',
  REFRESH = 'REFRESH',
  OTP = 'OTP',
  VERIFY = 'VERIFY',
  TWO_FA = 'TWO_FA',
  CHANGE_PASSWORD = 'CHANGE_PASSWORD',
  FORGOT_PASSWORD = 'FORGOT_PASSWORD',
}

export interface JwtPayload {
  id: number;
  sub: number; // Subject (usually user ID or employee ID)
  username?: string; // Username (optional)
  permissions?: string[]; // User permissions (optional)
  typeToken?: TokenType; // Token type (optional for backward compatibility)
  isAdmin?: boolean; // Admin flag (optional for backward compatibility)
  // Add other relevant payload fields
}

@Injectable()
export class JwtUtilService {
  private readonly logger = new Logger(JwtUtilService.name);
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Generates an access token for a regular user.
   * @param payload - Must contain user ID (sub), can include username and permissions.
   * @returns Object containing the signed access token and its expiration time in seconds.
   */
  generateUserAccessToken(payload: {
    sub: number;
    username?: string;
    permissions?: string[];
  }): { token: string; expiresInSeconds: number } {
    const fullPayload: JwtPayload = {
      id: payload.sub,
      sub: payload.sub,
      username: payload.username,
      permissions: payload.permissions,
      isAdmin: false, // User is not admin
      typeToken: TokenType.ACCESS,
    };

    // Lấy thời gian hết hạn từ cấu hình
    const expiryTime = this.configService.get<string>('JWT_ACCESS_TOKEN_EXPIRATION_TIME', '1d');
    const expiresInSeconds = this.parseExpiryTime(expiryTime);

    // Đảm bảo expiresIn là giá trị hợp lệ (số giây hoặc chuỗi thời gian)
    const token = this.jwtService.sign(fullPayload, {
      secret: this.configService.get<string>('JWT_SECRET'),
      expiresIn: expiryTime,
    });

    return { token, expiresInSeconds };
  }

  /**
   * Generates a verification token for a regular user.
   * @param payload - Must contain user ID (sub), can include username and permissions.
   * @returns Object containing the signed verification token and its expiration time in seconds.
   */
  generateUserVerifyToken(payload: {
    sub: number;
    username?: string;
    permissions?: string[];
  }): { token: string; expiresInSeconds: number } {
    const fullPayload: JwtPayload = {
      id: payload.sub,
      sub: payload.sub,
      username: payload.username,
      permissions: payload.permissions,
      isAdmin: false,
      typeToken: TokenType.VERIFY,
    };

    // Lấy thời gian hết hạn từ cấu hình
    const expiryTime = this.configService.get<string>('JWT_ACCESS_TOKEN_EXPIRATION_TIME', '1d');
    const expiresInSeconds = this.parseExpiryTime(expiryTime);

    // Đảm bảo expiresIn là giá trị hợp lệ (số giây hoặc chuỗi thời gian)
    const token = this.jwtService.sign(fullPayload, {
      secret: this.configService.get<string>('JWT_SECRET'),
      expiresIn: expiryTime,
    });

    return { token, expiresInSeconds };
  }


  generateUserForgotPasswordToken(payload: {
    sub: number;
    username?: string;
    permissions?: string[];
  }): { token: string; expiresInSeconds: number } {
    const fullPayload: JwtPayload = {
      id: payload.sub,
      sub: payload.sub,
      username: payload.username,
      permissions: payload.permissions,
      isAdmin: false,
      typeToken: TokenType.FORGOT_PASSWORD,
    };

    // Lấy thời gian hết hạn từ cấu hình
    const expiryTime = this.configService.get<string>('JWT_ACCESS_TOKEN_EXPIRATION_TIME', '1d');
    const expiresInSeconds = this.parseExpiryTime(expiryTime);

    // Đảm bảo expiresIn là giá trị hợp lệ (số giây hoặc chuỗi thời gian)
    const token = this.jwtService.sign(fullPayload, {
      secret: this.configService.get<string>('JWT_SECRET'),
      expiresIn: expiryTime,
    });

    return { token, expiresInSeconds };
  }

  /**
   * Generates a change password token for a regular user.
   * @param payload - Must contain user ID (sub), can include username and permissions.
   * @returns Object containing the signed verification token and its expiration time in seconds.
   */
  generateUserChangePasswordToken(payload: {
    sub: number;
    username?: string;
    permissions?: string[];
  }): { token: string; expiresInSeconds: number } {
    const fullPayload: JwtPayload = {
      id: payload.sub,
      sub: payload.sub,
      username: payload.username,
      permissions: payload.permissions,
      isAdmin: false,
      typeToken: TokenType.VERIFY,
    };

    // Lấy thời gian hết hạn từ cấu hình
    const expiryTime = this.configService.get<string>('JWT_ACCESS_TOKEN_EXPIRATION_TIME', '1d');
    const expiresInSeconds = this.parseExpiryTime(expiryTime);

    // Đảm bảo expiresIn là giá trị hợp lệ (số giây hoặc chuỗi thời gian)
    const token = this.jwtService.sign(fullPayload, {
      secret: this.configService.get<string>('JWT_SECRET'),
      expiresIn: expiryTime,
    });

    return { token, expiresInSeconds };
  }

  /**
   * Generates a 2 fa token for a regular user.
   * @param payload - Must contain user ID (sub), can include username and permissions.
   * @returns Object containing the signed verification token and its expiration time in seconds.
   */
  generateUserTokenTwoFA(payload: {
    sub: number;
    username?: string;
  }): { token: string; expiresInSeconds: number } {
    const fullPayload: JwtPayload = {
      id: payload.sub,
      sub: payload.sub,
      username: payload.username,
      isAdmin: false,
      typeToken: TokenType.VERIFY,
    };

    // Lấy thời gian hết hạn từ cấu hình
    const expiryTime = this.configService.get<string>('JWT_ACCESS_TOKEN_EXPIRATION_TIME', '1d');
    const expiresInSeconds = this.parseExpiryTime(expiryTime);

    // Đảm bảo expiresIn là giá trị hợp lệ (số giây hoặc chuỗi thời gian)
    const token = this.jwtService.sign(fullPayload, {
      secret: this.configService.get<string>('JWT_SECRET'),
      expiresIn: expiryTime,
    });

    return { token, expiresInSeconds };
  }

  /**
   * Generates an access token for an employee/admin.
   * @param payload - Must contain employee ID (sub), can include username and permissions.
   * @returns Object containing the signed access token with isAdmin=true and its expiration time in seconds.
   */
  generateEmployeeAccessToken(payload: {
    sub: number;
    username?: string;
    permissions?: string[];
  }): { token: string; expiresInSeconds: number } {
    const fullPayload: JwtPayload = {
      id: payload.sub,
      sub: payload.sub,
      username: payload.username,
      permissions: payload.permissions,
      isAdmin: true, // Employee/Admin is admin
      typeToken: TokenType.ACCESS,
    };

    // Lấy thời gian hết hạn từ cấu hình
    const expiryTime = this.configService.get<string>('JWT_ACCESS_TOKEN_EXPIRATION_TIME', '1d');
    const expiresInSeconds = this.parseExpiryTime(expiryTime);

    // Đảm bảo expiresIn là giá trị hợp lệ (số giây hoặc chuỗi thời gian)
    const token = this.jwtService.sign(fullPayload, {
      secret: this.configService.get<string>('JWT_SECRET'),
      expiresIn: expiryTime,
    });

    return { token, expiresInSeconds };
  }

  /**
   * Generates a refresh token.
   * @param payload - The payload to sign (must include user/employee ID and isAdmin status, can include username and permissions).
   * @returns Object containing the signed refresh token and its expiration time in seconds.
   */
  generateRefreshToken(payload: JwtPayload): { token: string; expiresInSeconds: number } {
    const fullPayload: JwtPayload = {
      id: payload.sub,
      sub: payload.sub,
      username: payload.username,
      permissions: payload.permissions,
      isAdmin: payload.isAdmin ? payload.isAdmin : false,
      typeToken: TokenType.REFRESH,
    };

    // Lấy thời gian hết hạn từ cấu hình
    const expiryTime = this.configService.get<string>('JWT_REFRESH_TOKEN_EXPIRATION_TIME', '7d');
    const expiresInSeconds = this.parseExpiryTime(expiryTime);

    // Đảm bảo expiresIn là giá trị hợp lệ (số giây hoặc chuỗi thời gian)
    const token = this.jwtService.sign(fullPayload, {
      secret: this.configService.get<string>('JWT_SECRET'),
      expiresIn: expiryTime,
    });

    return { token, expiresInSeconds };
  }

  /**
   * Generates a custom token with specified expiry time.
   * @param payload - The payload to sign
   * @param tokenType - The type of token
   * @param expiryTime - Custom expiry time (e.g., '30d', '60m')
   * @returns Object containing the signed token and its expiration time in seconds.
   */
  generateCustomToken(
    payload: JwtPayload,
    tokenType: TokenType,
    expiryTime: string
  ): { token: string; expiresInSeconds: number } {
    const fullPayload: JwtPayload = {
      id: payload.sub,
      sub: payload.sub,
      username: payload.username,
      permissions: payload.permissions,
      isAdmin: payload.isAdmin ? payload.isAdmin : false,
      typeToken: tokenType,
    };

    const expiresInSeconds = this.parseExpiryTime(expiryTime);

    // Đảm bảo expiresIn là giá trị hợp lệ (số giây hoặc chuỗi thời gian)
    const token = this.jwtService.sign(fullPayload, {
      secret: this.configService.get<string>('JWT_SECRET'),
      expiresIn: expiryTime,
    });

    return { token, expiresInSeconds };
  }

  /**
   * Phân tích chuỗi thời gian hết hạn (như '7d', '24h', '30m', hoặc số giây) thành số giây
   * @param expiryTime Chuỗi thời gian hết hạn hoặc số giây
   * @returns Số giây
   */
  private parseExpiryTime(expiryTime: string | number): number {
    // Nếu expiryTime là số, trả về trực tiếp
    if (typeof expiryTime === 'number') {
      return expiryTime;
    }

    // Nếu expiryTime là chuỗi số, chuyển đổi thành số và trả về
    if (/^\d+$/.test(expiryTime)) {
      return parseInt(expiryTime, 10);
    }

    // Xử lý các định dạng chuỗi thời gian như '7d', '24h', '30m', '60s'
    const match = expiryTime.match(/^(\d+)([dhms])$/);
    if (!match) {
      this.logger.warn(`Invalid expiry time format: ${expiryTime}, using default 1 day`);
      return 24 * 60 * 60; // 1 day in seconds as default
    }

    const value = parseInt(match[1], 10);
    const unit = match[2];

    switch (unit) {
      case 'd': return value * 24 * 60 * 60; // days to seconds
      case 'h': return value * 60 * 60;      // hours to seconds
      case 'm': return value * 60;           // minutes to seconds
      case 's': return value;                // seconds
      default: return 24 * 60 * 60;          // default: 1 day
    }
  }

  /**
   * Verifies a token's signature and expiration.
   * @param token - The JWT to verify.
   * @param expectedType - Optional: The expected TokenType.
   * @returns The decoded payload if valid and matches expected type.
   */
  verifyToken(token: string, expectedType?: TokenType): any {
    try {
      // Thử xác thực token với secret key
      try {
        const payload = this.jwtService.verify(token, {
          secret: this.configService.get<string>('JWT_SECRET'),
        });

        // Chỉ kiểm tra typeToken nếu expectedType được cung cấp và payload có trường typeToken
        if (
          expectedType &&
          payload.typeToken &&
          payload.typeToken !== expectedType
        ) {
          throw new Error(
            `Invalid token type. Expected ${expectedType}, got ${payload.typeToken}`,
          );
        }

        // Đảm bảo trường isAdmin tồn tại
        if (typeof payload.isAdmin !== 'boolean') {
          console.warn(
            'Token payload missing isAdmin field. Treating as non-admin.',
          );
          payload.isAdmin = payload.role === 'admin'; // Dựa vào role để xác định isAdmin
        }

        // Đảm bảo trường typeToken tồn tại
        if (!payload.typeToken) {
          console.warn(
            'Token payload missing typeToken field. Treating as ACCESS token.',
          );
          payload.typeToken = 'ACCESS';
        }

        // Đảm bảo trường sub tồn tại
        if (typeof payload.sub !== 'number' && payload.sub === undefined) {
          console.warn('Token payload missing sub field.');
        }

        return payload;
      } catch (verifyError) {
        // Nếu xác thực thất bại và đang ở môi trường phát triển, giải mã token mà không xác thực
        if (this.configService.get<string>('NODE_ENV') === 'development') {
          console.warn(
            'DEVELOPMENT MODE: Bypassing token signature verification',
          );
          const payload = this.jwtService.decode(token);

          if (!payload) {
            throw new Error('Invalid token format');
          }

          // Đảm bảo trường isAdmin tồn tại
          if (typeof payload.isAdmin !== 'boolean') {
            console.warn(
              'Token payload missing isAdmin field. Treating as non-admin.',
            );
            payload['isAdmin'] = payload['role'] === 'admin'; // Dựa vào role để xác định isAdmin
          }

          // Đảm bảo trường typeToken tồn tại
          if (!payload['typeToken']) {
            console.warn(
              'Token payload missing typeToken field. Treating as ACCESS token.',
            );
            payload['typeToken'] = 'ACCESS';
          }

          return payload;
        } else {
          // Ở môi trường production, tiếp tục ném lỗi
          throw verifyError;
        }
      }
    } catch (error) {
      console.error('Error verifying token:', error.message);
      throw error;
    }
  }

  /**
   * Specifically verifies a User Access Token.
   * Checks for validity, expected token type, expiration time, and isAdmin=false flag.
   * @param token - The JWT string to verify.
   * @param expectedType - Optional: The expected TokenType (defaults to ACCESS).
   * @returns The decoded JwtPayload if valid user token.
   * @throws {UnauthorizedException} If token is invalid, wrong type, expired, or isAdmin is true.
   */
  verifyTokenUser(token: string, expectedType: TokenType = TokenType.ACCESS): any {
    try {
      // Verify token and get payload
      const payload = this.verifyToken(token);

      // Kiểm tra loại token có phải là loại mong đợi không
      if (payload.typeToken !== expectedType) {
        console.warn(
          `Invalid token type. Expected ${expectedType} token but got:`,
          payload.typeToken,
        );
        throw new AppException(ErrorCode.TOKEN_INVALID_OR_EXPIRED, `Invalid token type. ${expectedType} token required.`);
      }

      // Kiểm tra thời gian hết hạn
      const currentTime = Math.floor(Date.now() / 1000);
      if (payload.exp && payload.exp < currentTime) {
        console.warn('Token has expired. Expiration time:', new Date(payload.exp * 1000));
        throw new AppException(ErrorCode.TOKEN_INVALID_OR_EXPIRED, 'Token has expired.');
      }

      // Kiểm tra quyền người dùng (không phải admin)
      if (payload.isAdmin === true) {
        console.warn(
          'Access denied. User privileges required. isAdmin:',
          payload.isAdmin,
        );
        throw new AppException(ErrorCode.UNAUTHORIZED_ACCESS, 'Access denied. User privileges required.');
      }

      return payload;
    } catch (error) {
      // Nếu lỗi đã được xử lý trong hàm, chuyển tiếp lỗi
      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý các lỗi khác
      console.error('Error verifying user token:', error.message);
      throw new AppException(ErrorCode.TOKEN_INVALID_OR_EXPIRED, 'Invalid user token.');
    }
  }

  /**
   * Specifically verifies an Employee Access Token.
   * Checks for validity, ACCESS type, expiration time, and isAdmin=true flag.
   * @param token - The JWT string to verify.
   * @returns The decoded JwtPayload if valid employee access token.
   * @throws {UnauthorizedException} If token is invalid, not an ACCESS token, expired, or isAdmin is not true.
   */
  verifyEmployeeAccessToken(token: string): any {
    try {
      // Verify token and get payload
      const payload = this.verifyToken(token);

      // Kiểm tra loại token có phải là ACCESS không
      if (payload.typeToken !== TokenType.ACCESS) {
        console.warn(
          'Invalid token type. Expected ACCESS token but got:',
          payload.typeToken,
        );
        throw new AppException(ErrorCode.TOKEN_INVALID_OR_EXPIRED, 'Invalid token type. ACCESS token required.');
      }

      // Kiểm tra thời gian hết hạn
      const currentTime = Math.floor(Date.now() / 1000);
      if (payload.exp && payload.exp < currentTime) {
        console.warn('Token has expired. Expiration time:', new Date(payload.exp * 1000));
        throw new AppException(ErrorCode.TOKEN_INVALID_OR_EXPIRED, 'Token has expired.');
      }

      // Kiểm tra quyền admin
      if (payload.isAdmin !== true) {
        console.warn(
          'Access denied. Admin privileges required. User role:',
          payload.role,
          'isAdmin:',
          payload.isAdmin,
        );
        throw new AppException(ErrorCode.UNAUTHORIZED_ACCESS, 'Access denied. Admin privileges required.');
      }

      return payload;
    } catch (error) {
      // Nếu lỗi đã được xử lý trong hàm, chuyển tiếp lỗi
      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý các lỗi khác
      console.error('Error verifying employee access token:', error.message);
      throw new AppException(ErrorCode.TOKEN_INVALID_OR_EXPIRED, 'Invalid employee access token.');
    }
  }

  /**
   * Decodes a token without verifying its signature or expiration.
   * @param token - The JWT to decode.
   * @returns The decoded payload or null if invalid format.
   */
  decodeToken(token: string): any {
    // Decode token without type assertion
    return this.jwtService.decode(token);
  }
}