import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng agents_base trong cơ sở dữ liệu
 * Bảng lưu trạng thái đặc biệt của agent, đánh dấu agent đang hoạt động hoặc thuộc nhóm cơ bản
 */
@Entity('agents_base')
export class AgentBase {
  /**
   * UUID tham chiếu từ agents.id
   */
  @PrimaryColumn('uuid')
  id: string;

  /**
   * ID nhân viên tạo
   */
  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  /**
   * ID nhân viên cập nhật
   */
  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;

  /**
   * ID nhân viên xóa
   */
  @Column({ name: 'deleted_by', nullable: true })
  deletedBy: number;

  /**
   * Trạng thái hoạt động (chỉ một agent có active = TRUE tại một thời điểm)
   */
  @Column({ type: 'boolean', default: false })
  active: boolean;

  /**
   * Tên của model (được lấy từ model_registry)
   */
  @Column({ name: 'model_name', type: 'varchar', length: 255, nullable: true })
  modelName: string | null;

  /**
   * UUID tham chiếu đến bảng model_registry, mô tả khả năng của mô hình
   */
  @Column({ name: 'model_registry_id', type: 'uuid', nullable: true })
  modelRegistryId: string | null;

  /**
   * UUID tham chiếu đến bảng system_key_llm, chứa khóa API dùng để gọi mô hình AI
   */
  @Column({ name: 'llm_key_id', type: 'uuid', nullable: true })
  llmKeyId: string | null;

  /**
   * UUID tham chiếu đến bảng model_base
   */
  @Column({ name: 'model_base_id', type: 'uuid', nullable: true })
  modelBaseId: string | null;

  /**
   * UUID tham chiếu đến bảng model_fine_tuning
   */
  @Column({ name: 'model_finetuning_id', type: 'uuid', nullable: true })
  modelFinetuningId: string | null;

  /**
   * Thời điểm xóa mềm (timestamp millis)
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;
}
