import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { MultiAgentsSystem } from '@modules/agent/entities';

/**
 * DTO cho thông tin quan hệ multi-agent trong response
 */
export class MultiAgentRelationResponseDto {
  /**
   * ID của agent cấ<PERSON> trên (parent)
   */
  @ApiProperty({
    description: 'ID của agent cấp trên (parent)',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  parentAgentId: string;

  /**
   * ID của agent cấp dướ<PERSON> (child)
   */
  @ApiProperty({
    description: 'ID của agent cấp dướ<PERSON> (child)',
    example: '123e4567-e89b-12d3-a456-426614174001',
  })
  childAgentId: string;

  /**
   * Tên của agent cấp dướ<PERSON> (child)
   */
  @ApiPropertyOptional({
    description: 'Tên của agent cấp dướ<PERSON> (child)',
    example: 'System Assistant',
  })
  childAgentName?: string;

  /**
   * Prompt cho quan hệ giữa hai agent
   */
  @ApiPropertyOptional({
    description: 'Prompt cho quan hệ giữa hai agent',
    example: 'Bạn là trợ lý của agent cấp trên, hãy hỗ trợ theo yêu cầu',
  })
  prompt: string | null;

  /**
   * Chuyển đổi từ entity sang DTO
   * @param entity Entity MultiAgentsSystem
   * @returns MultiAgentRelationResponseDto
   */
  static fromEntity(entity: MultiAgentsSystem, childAgentName?: string): MultiAgentRelationResponseDto {
    const dto = new MultiAgentRelationResponseDto();
    dto.parentAgentId = entity.parentAgentId;
    dto.childAgentId = entity.childAgentId;
    dto.prompt = entity.prompt;
    
    if (childAgentName) {
      dto.childAgentName = childAgentName;
    }
    
    return dto;
  }
}
