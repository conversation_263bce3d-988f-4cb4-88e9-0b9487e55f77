import { PaginatedResult } from '@/common/response';
import { AppException, ErrorCode } from '@common/exceptions';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { AgentBaseRepository } from '@modules/agent/repositories/agent-base.repository';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { S3Service } from '@shared/services/s3.service';
import { Transactional } from 'typeorm-transactional';
import {
  AgentBaseListItemDto,
  AgentBaseQueryDto,
  AgentBaseResponseDto,
  AgentBaseTrashItemDto,
  CreateAgentBaseDto,
  UpdateAgentBaseDto,
  ModelInfoDto
} from '../dto/agent-base';
import { ModelConfig } from '@modules/agent/interfaces/model-config.interface';
import { AvatarUrlHelper } from '../helpers/avatar-url.helper';
import { AgentBaseMapper } from '../mappers/agent-base.mapper';
import { VectorStoreRepository } from '@modules/data/knowledge-files/repositories/vector-store.repository';
import { KNOWLEDGE_FILE_ERROR_CODES } from '@modules/data/knowledge-files/exceptions/knowledge-file.exception';
import { AdminModelBaseService } from '@modules/models/admin/services/admin-model-base.service';


/**
 * Service xử lý logic nghiệp vụ liên quan đến agent base
 */
@Injectable()
export class AdminAgentBaseService {
  private readonly logger = new Logger(AdminAgentBaseService.name);

  constructor(
    private readonly agentBaseRepository: AgentBaseRepository,
    private readonly agentRepository: AgentRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly vectorStoreRepository: VectorStoreRepository,
    private readonly adminModelBaseService: AdminModelBaseService,
  ) { }

  /**
   * Lấy danh sách agent base với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent base với phân trang
   */
  // async findAll(
  //   queryDto: AgentBaseQueryDto,
  // ): Promise<PaginatedResult<AgentBaseListItemDto>> {
  //   try {
  //     const {
  //       page = 1,
  //       limit = 10,
  //       search,
  //       active,
  //       sortBy = 'createdAt',
  //       sortDirection = 'DESC',
  //     } = queryDto;

  //     const result = await this.agentBaseRepository.findPaginated(
  //       page,
  //       limit,
  //       search,
  //       active,
  //       sortBy,
  //       sortDirection,
  //     );

  //     // Chuyển đổi từ entity sang DTO sử dụng mapper
  //     const items = await Promise.all(
  //       result.items.map(async (agentBase) => {
  //         // Tìm agent tương ứng với agentBase
  //         const agent = result.agents?.find((a) => a.id === agentBase.id);

  //         if (!agent) {
  //           throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
  //         }

  //         // Tạo thông tin model từ model_base_id hoặc model_finetuning_id
  //         const modelInfo = await this.createModelInfo(agentBase);

  //         // Sử dụng mapper để chuyển đổi
  //         return AgentBaseMapper.toListItemDto(agentBase, agent, this.cdnService, modelInfo);
  //       })
  //     );

  //     return {
  //       items,
  //       meta: result.meta,
  //     };
  //   } catch (error) {
  //     this.logger.error(
  //       `Error finding agent bases: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
  //   }
  // }

  // /**
  //  * Lấy thông tin chi tiết agent base theo ID
  //  * @param id ID của agent base
  //  * @returns Thông tin chi tiết agent base
  //  */
  // async findById(id: string): Promise<AgentBaseResponseDto> {

  //   const agentBase = await this.agentBaseRepository.findById(id);
  //   if (!agentBase) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND);
  //   }

  //   // Kiểm tra xem agent base đã bị xóa chưa (sử dụng deletedBy thay vì deletedAt)
  //   if (agentBase.deletedBy) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND, 'Agent base đã bị xóa');
  //   }

  //   try {
  //     // Lấy thông tin agent tương ứng
  //     const agent = await this.agentRepository.findById(id);
  //     if (!agent) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
  //     }

  //     // Multi-agent relations đã được bỏ khỏi API
  //     const multiAgentRelations: any[] = [];

  //     // Tạo thông tin model từ model_base_id hoặc model_finetuning_id
  //     const modelInfo = await this.createModelInfo(agentBase);

  //     // Sử dụng mapper để chuyển đổi sang DTO
  //     return await AgentBaseMapper.toResponseDto(
  //       agentBase,
  //       agent,
  //       undefined,
  //       this.cdnService,
  //       this.employeeInfoService,
  //       multiAgentRelations,
  //       modelInfo,
  //     );
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error finding agent base: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
  //   }
  // }

  // /**
  //  * Tạo agent base mới
  //  * @param createDto Dữ liệu tạo agent base
  //  * @param employeeId ID của nhân viên tạo
  //  * @returns Thông tin agent base đã tạo
  //  */
  // @Transactional()
  // async create(
  //   createDto: CreateAgentBaseDto,
  //   employeeId: number,
  // ): Promise<string | null> {
  //   try {
  //     // Validation model sẽ được thực hiện thông qua DTO validator

  //     // Validate vector store nếu có
  //     if (createDto.vectorStoreId) {
  //       await this.validateVectorStore(createDto.vectorStoreId);
  //     }

  //     // Tạo agent mới
  //     const agent = this.agentRepository.create({
  //       name: createDto.name,
  //       modelConfig: createDto.modelConfig,
  //       instruction: createDto.instruction,
  //     });

  //     // Tạo URL tải lên avatar (nếu có)
  //     let avatarUrlUpload: { url: string; key: string | null } | undefined;
  //     if (createDto.avatarMimeType) {
  //       // Tạo URL tải lên avatar
  //       avatarUrlUpload = await AvatarUrlHelper.generateUploadUrl(
  //         this.s3Service,
  //         employeeId.toString(),
  //         createDto.avatarMimeType,
  //         agent.id,
  //       );

  //       // Cập nhật avatar key cho agent nếu có key mới
  //       if (avatarUrlUpload.key) {
  //         agent.avatar = avatarUrlUpload.key;
  //       }
  //     }

  //     // Lưu agent
  //     const savedAgent = await this.agentRepository.save(agent);

  //     // Tạo agent base mới
  //     const agentBase = this.agentBaseRepository.create({
  //       id: savedAgent.id,
  //       createdBy: employeeId,
  //       updatedBy: employeeId,
  //       active: false, // Mặc định không active
  //     });

  //     // Nếu active = true, đảm bảo chỉ có một agent base active
  //     if (createDto.active) {
  //       // Trước khi set active = true, cần deactivate tất cả agent base khác
  //       await this.agentBaseRepository.deactivateAll();
  //       agentBase.active = createDto.active;
  //     }

  //     // Lưu agent base trước
  //     const createdAgentBase = await this.agentBaseRepository.save(agentBase);


  //     if (!createdAgentBase) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND);
  //     }

  //     // Multi-agent relations đã được bỏ khỏi API

  //     return avatarUrlUpload ? avatarUrlUpload.url : null;
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error creating agent base: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
  //   }
  // }

  // /**
  //  * Cập nhật thông tin agent base
  //  * @param id ID của agent base
  //  * @param updateDto Dữ liệu cập nhật
  //  * @param employeeId ID của nhân viên cập nhật
  //  * @returns Thông tin agent base đã cập nhật
  //  */
  // @Transactional()
  // async update(
  //   id: string,
  //   updateDto: UpdateAgentBaseDto,
  //   employeeId: number,
  // ): Promise<string | null> {
  //   // Kiểm tra agent base có tồn tại không
  //   const agentBaseExists = await this.agentBaseRepository.existsById(id);
  //   if (!agentBaseExists) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND);
  //   }

  //   // Kiểm tra agent có tồn tại không
  //   const agentExists = await this.agentRepository.existsById(id);
  //   if (!agentExists) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
  //   }

  //   // Sau khi đã kiểm tra tồn tại, lấy thông tin chi tiết
  //   // Lấy thông tin agent (cần cho avatar)
  //   const agent = await this.agentRepository.findById(id);
  //   if (!agent) {
  //     // Trường hợp này không nên xảy ra vì đã kiểm tra tồn tại ở trên
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
  //   }

  //   // Lấy thông tin agent base (cần cho cập nhật)
  //   const agentBase = await this.agentBaseRepository.findById(id);
  //   if (!agentBase) {
  //     // Trường hợp này không nên xảy ra vì đã kiểm tra tồn tại ở trên
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND);
  //   }

  //   try {
  //     // Cập nhật thông tin agent nếu có
  //     const agentUpdates: Partial<{
  //       name: string;
  //       instruction: string | null;
  //       modelConfig: ModelConfig;
  //       vectorStoreId: string | null;
  //       status: AgentStatusEnum;
  //       avatar: string;
  //       updatedAt: number;
  //       rejectionReason?: string | null;
  //     }> = {};
  //     let hasAgentUpdates = false;

  //     if (updateDto.name !== undefined) {
  //       agentUpdates.name = updateDto.name;
  //       hasAgentUpdates = true;
  //     }

  //     if (updateDto.instruction !== undefined) {
  //       agentUpdates.instruction = updateDto.instruction;
  //       hasAgentUpdates = true;
  //     }

  //     if (updateDto.modelConfig !== undefined) {
  //       // Model validation sẽ được thực hiện thông qua DTO validator
  //       agentUpdates.modelConfig = updateDto.modelConfig;
  //       hasAgentUpdates = true;
  //     }

  //     if (updateDto.vectorStoreId !== undefined) {
  //       // Validate vector store nếu có giá trị (không phải null)
  //       if (updateDto.vectorStoreId) {
  //         await this.validateVectorStore(updateDto.vectorStoreId);
  //       }
  //       agentUpdates.vectorStoreId = updateDto.vectorStoreId;
  //       hasAgentUpdates = true;
  //     }

  //     // Status field đã được bỏ khỏi UpdateAgentBaseDto

  //     // Tạo URL tải lên avatar mới (nếu có)
  //     let avatarUrlUpload: { url: string; key: string | null } | undefined;
  //     if (updateDto.avatarMimeType) {
  //       // Tạo URL tải lên avatar
  //       avatarUrlUpload = await AvatarUrlHelper.generateUploadUrl(
  //         this.s3Service,
  //         employeeId.toString(),
  //         updateDto.avatarMimeType,
  //         id,
  //         agent.avatar || undefined,
  //       );

  //       // Cập nhật avatar key cho agent nếu có key mới
  //       if (avatarUrlUpload.key) {
  //         agentUpdates.avatar = avatarUrlUpload.key;
  //         hasAgentUpdates = true;
  //       }
  //     }

  //     // Lưu thông tin agent nếu có cập nhật
  //     if (hasAgentUpdates) {
  //       await this.agentRepository.update(id, agentUpdates);
  //     }

  //     // Cập nhật thông tin agent base
  //     agentBase.updatedBy = employeeId;

  //     // Nếu có cập nhật trạng thái active
  //     if (updateDto.active !== undefined) {
  //       if (updateDto.active) {
  //         // Nếu đặt thành active, trước tiên deactivate tất cả agent base khác
  //         await this.agentBaseRepository.deactivateAll();
  //         agentBase.active = true;
  //       } else {
  //         // Nếu đặt thành không active, chỉ cập nhật trạng thái
  //         agentBase.active = false;
  //       }
  //     }

  //     // Lưu thông tin agent base
  //     await this.agentBaseRepository.save(agentBase);

  //     return avatarUrlUpload ? avatarUrlUpload.url : null;
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error updating agent base: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
  //   }
  // }

  // /**
  //  * Xóa agent base (soft delete)
  //  * @param id ID của agent base
  //  * @param employeeId ID của nhân viên xóa
  //  */
  // @Transactional()
  // async remove(id: string, employeeId: number): Promise<void> {
  //   // Kiểm tra agent base có tồn tại không
  //   const agentBaseExists = await this.agentBaseRepository.existsById(id);
  //   if (!agentBaseExists) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND);
  //   }

  //   try {
  //     // Kiểm tra agent có tồn tại không
  //     const agentExists = await this.agentRepository.existsById(id);

  //     // Xóa mềm agent base bằng cách cập nhật trường deletedBy
  //     const agentBaseDeleted = await this.agentBaseRepository.customSoftDelete(id, employeeId);

  //     if (!agentBaseDeleted) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_DELETE_FAILED, 'Không thể xóa agent');
  //     }

  //     this.logger.debug(`Đã xóa mềm agent base với ID ${id}`);

  //     // Nếu có agent tương ứng, cũng xóa mềm agent
  //     if (agentExists) {
  //       // Xóa mềm agent bằng cách cập nhật trường deletedAt
  //       const agentDeleted = await this.agentRepository.customSoftDelete(id);
  //       if (!agentDeleted) {
  //         throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_DELETE_FAILED, 'Không thể xóa agent');
  //       }
  //     } else {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND, 'Không tìm thấy agent');
  //     }
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error removing agent base: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_DELETE_FAILED);
  //   }
  // }

  // /**
  //  * Đặt agent base thành active
  //  * @param id ID của agent base
  //  * @param employeeId ID của nhân viên cập nhật
  //  */
  // @Transactional()
  // async setActive(id: string): Promise<void> {
  //   // Kiểm tra agent base có tồn tại không
  //   const agentBaseExists = await this.agentBaseRepository.existsById(id);
  //   if (!agentBaseExists) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND);
  //   }

  //   // Lấy thông tin agent base để cập nhật
  //   const agentBase = await this.agentBaseRepository.findById(id);
  //   if (!agentBase) {
  //     // Trường hợp này không nên xảy ra vì đã kiểm tra tồn tại ở trên
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND);
  //   }
  //   try {

  //     // Đặt agent base thành active
  //     await this.agentBaseRepository.setActive(id);

  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error setting agent base active: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
  //   }
  // }

  // /**
  //  * Kiểm tra vector store có tồn tại không
  //  * @param vectorStoreId ID của vector store
  //  * @throws AppException nếu vector store không tồn tại
  //  */
  // private async validateVectorStore(vectorStoreId: string): Promise<void> {
  //   try {
  //     this.logger.debug(`Validating vector store: ${vectorStoreId}`);

  //     // Kiểm tra vector store có tồn tại không (admin có thể truy cập tất cả vector store)
  //     const vectorStore = await this.vectorStoreRepository.findOne({
  //       where: { id: vectorStoreId }
  //     });

  //     if (!vectorStore) {
  //       this.logger.warn(`Vector store ${vectorStoreId} không tồn tại`);
  //       throw new AppException(
  //         KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
  //         `Vector store với ID ${vectorStoreId} không tồn tại`
  //       );
  //     }

  //     this.logger.debug(`Vector store ${vectorStoreId} hợp lệ: ${vectorStore.name}`);
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error validating vector store: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND);
  //   }
  // }

  // /**
  //  * Tạo thông tin model từ model_base_id hoặc model_finetuning_id
  //  * @param agentBase Entity AgentBase
  //  * @returns ModelInfoDto hoặc undefined
  //  */
  // private async createModelInfo(agentBase: any): Promise<ModelInfoDto | undefined> {
  //   try {
  //     const modelInfo: ModelInfoDto = {
  //       model_base_id: agentBase.modelBaseId,
  //       model_finetuning_id: agentBase.modelFinetuningId,
  //       model_id: null,
  //       typeProvider: null,
  //     };

  //     // Nếu có model_base_id, lấy thông tin từ base model
  //     if (agentBase.modelBaseId) {
  //       try {
  //         const baseModelResponse = await this.adminModelBaseService.findOne(agentBase.modelBaseId);
  //         const baseModel = baseModelResponse.result;
  //         if (baseModel) {
  //           modelInfo.model_id = baseModel.modelId;
  //           modelInfo.typeProvider = baseModel.provider;
  //         }
  //       } catch (error) {
  //         this.logger.warn(`Không thể lấy thông tin base model ${agentBase.modelBaseId}: ${error.message}`);
  //       }
  //     }

  //     // Nếu có model_finetuning_id, lấy thông tin từ finetuning model
  //     // TODO: Implement finetuning model service khi có
  //     if (agentBase.modelFinetuningId) {
  //       this.logger.debug(`Finetuning model service chưa được implement cho ID: ${agentBase.modelFinetuningId}`);
  //     }

  //     return modelInfo;
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi tạo thông tin model: ${error.message}`, error.stack);
  //     return undefined;
  //   }
  // }

  // /**
  //  * Lấy danh sách agent base đã xóa với phân trang
  //  * @param queryDto Tham số truy vấn
  //  * @returns Danh sách agent base đã xóa với phân trang
  //  */
  // async findAllDeleted(
  //   queryDto: AgentBaseQueryDto,
  // ): Promise<PaginatedResult<AgentBaseTrashItemDto>> {
  //   try {
  //     const {
  //       page = 1,
  //       limit = 10,
  //       search,
  //       sortBy = 'deletedBy',
  //       sortDirection = 'DESC',
  //     } = queryDto;

  //     const result = await this.agentBaseRepository.findDeletedPaginated(
  //       page,
  //       limit,
  //       search,
  //       sortBy,
  //       sortDirection,
  //     );

  //     // Tạo map để lookup employee info nhanh hơn
  //     const employeeMap = new Map();
  //     if (result.deletedEmployees) {
  //       result.deletedEmployees.forEach(emp => {
  //         employeeMap.set(emp.agentBaseId, emp);
  //       });
  //     }

  //     // Batch tạo model info để tránh N+1 queries
  //     const modelInfoMap = new Map();
  //     const uniqueModelBaseIds = [...new Set(result.items.map(item => item.modelBaseId).filter(Boolean))];

  //     if (uniqueModelBaseIds.length > 0) {
  //       await Promise.all(
  //         uniqueModelBaseIds.map(async (modelBaseId) => {
  //           try {
  //             const modelInfo = await this.createModelInfo({ modelBaseId });
  //             modelInfoMap.set(modelBaseId, modelInfo);
  //           } catch (error) {
  //             this.logger.warn(`Không thể lấy thông tin model ${modelBaseId}: ${error.message}`);
  //           }
  //         })
  //       );
  //     }

  //     // Chuyển đổi từ entity sang DTO
  //     const items = result.items.map((agentBase) => {
  //       // Tìm agent tương ứng với agentBase
  //       const agent = result.agents?.find((a) => a.id === agentBase.id);

  //       if (!agent) {
  //         throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
  //       }

  //       const dto = new AgentBaseTrashItemDto();
  //       dto.id = agent.id;
  //       dto.name = agent.name;
  //       dto.avatar = agent.avatar
  //         ? AvatarUrlHelper.generateViewUrl(this.cdnService, agent.avatar)
  //         : null;

  //       // Lấy thông tin model từ cache
  //       const modelInfo = modelInfoMap.get(agentBase.modelBaseId);
  //       if (modelInfo) {
  //         dto.model_id = modelInfo.model_id;
  //         dto.type_provider = modelInfo.typeProvider;
  //         dto.model = modelInfo.model_id || 'Unknown Model';
  //       } else {
  //         dto.model_id = null;
  //         dto.type_provider = null;
  //         dto.model = 'Unknown Model';
  //       }

  //       // Thông tin người xóa từ JOIN data (không cần gọi service)
  //       const employeeInfo = employeeMap.get(agentBase.id);
  //       if (employeeInfo) {
  //         dto.deleted = {
  //           employeeId: employeeInfo.employeeId,
  //           name: employeeInfo.employeeName,
  //           avatar: employeeInfo.employeeAvatar,
  //           date: agent.deletedAt || Date.now(),
  //         };
  //       }

  //       return dto;
  //     });

  //     return {
  //       items,
  //       meta: result.meta,
  //     };
  //   } catch (error) {
  //     this.logger.error(
  //       `Error finding deleted agent bases: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
  //   }
  // }

  // /**
  //  * Khôi phục agent base đã xóa
  //  * @param id ID của agent base cần khôi phục
  //  * @param employeeId ID của nhân viên thực hiện khôi phục
  //  */
  // @Transactional()
  // async restore(id: string, employeeId: number): Promise<void> {
  //   // Kiểm tra agent base đã xóa có tồn tại không
  //   const deletedAgentBase = await this.agentBaseRepository.findDeletedById(id);
  //   if (!deletedAgentBase) {
  //     throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND, 'Agent base đã xóa không tồn tại');
  //   }

  //   try {
  //     // Khôi phục agent base
  //     const agentBaseRestored = await this.agentBaseRepository.restoreAgentBase(id, employeeId);
  //     if (!agentBaseRestored) {
  //       throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND, 'Không thể khôi phục agent base');
  //     }

  //     // Kiểm tra và khôi phục agent tương ứng nếu có (bao gồm cả agent đã bị xóa)
  //     const agentExists = await this.agentRepository.existsByIdIncludingDeleted(id);
  //     if (agentExists) {
  //       const agentRestored = await this.agentRepository.restoreAgents([id]);
  //       if (agentRestored === 0) {
  //         this.logger.warn(`Không thể khôi phục agent với ID ${id}`);
  //       } else {
  //         this.logger.debug(`Đã khôi phục agent với ID ${id}`);
  //       }
  //     } else {
  //       this.logger.debug(`Không tìm thấy agent với ID ${id} để khôi phục`);
  //     }

  //     this.logger.debug(`Đã khôi phục agent base với ID ${id}`);
  //   } catch (error) {
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     this.logger.error(
  //       `Error restoring agent base: ${error.message}`,
  //       error.stack,
  //     );
  //     throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
  //   }
  // }

  // Removed unused methods: createMultiAgentRelations, validateModel
}
