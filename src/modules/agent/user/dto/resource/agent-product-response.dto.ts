import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin sản phẩm của agent
 */
export class AgentProductResponseDto {
  /**
   * ID của sản phẩm
   */
  @ApiProperty({
    description: 'ID của sản phẩm',
    example: 'p1r2o3d4-1',
  })
  id: string;

  /**
   * Tên sản phẩm
   */
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Product 1',
  })
  name: string;

  /**
   * URL hình ảnh sản phẩm
   */
  @ApiProperty({
    description: 'URL hình ảnh sản phẩm',
    example: 'https://cdn.example.com/products/product-1.jpg',
  })
  imageUrl: string;

  /**
   * Thời điểm tạo (timestamp millis)
   */
  @ApiProperty({
    description: 'Thời điểm tạo (timestamp millis)',
    example: 1672531200000,
  })
  createdAt: number;
}
