import { Test, TestingModule } from '@nestjs/testing';
import { ValidationHelper } from './validation.helper';
import { AppException } from '@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';
import { ProductCategory } from '@modules/marketplace/enums';

describe('ValidationHelper - Source Resource Status Validation', () => {
  let validationHelper: ValidationHelper;
  let mockKnowledgeFileRepository: any;
  let mockAgentRepository: any;
  let mockUserDataFineTuneRepository: any;
  let mockAdminDataFineTuneRepository: any;
  let mockStrategyRepository: any;

  beforeEach(async () => {
    // Mock repositories
    mockKnowledgeFileRepository = {
      findOne: jest.fn(),
    };
    mockAgentRepository = {
      findOne: jest.fn(),
    };
    mockUserDataFineTuneRepository = {
      findOne: jest.fn(),
    };
    mockAdminDataFineTuneRepository = {
      findOne: jest.fn(),
    };
    mockStrategyRepository = {
      findOne: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [ValidationHelper],
    }).compile();

    validationHelper = module.get<ValidationHelper>(ValidationHelper);
  });

  describe('validateSourceResourceStatus', () => {
    it('nên bỏ qua validation khi không có sourceId', async () => {
      await expect(
        validationHelper.validateSourceResourceStatus(
          null,
          ProductCategory.KNOWLEDGE_FILE,
          {
            knowledgeFileRepository: mockKnowledgeFileRepository,
          }
        )
      ).resolves.not.toThrow();
    });

    it('nên pass khi Knowledge File có status APPROVED', async () => {
      mockKnowledgeFileRepository.findOne.mockResolvedValue({
        id: 'test-id',
        status: 'APPROVED'
      });

      await expect(
        validationHelper.validateSourceResourceStatus(
          'test-id',
          ProductCategory.KNOWLEDGE_FILE,
          {
            knowledgeFileRepository: mockKnowledgeFileRepository,
          }
        )
      ).resolves.not.toThrow();

      expect(mockKnowledgeFileRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'test-id' },
        select: ['id', 'status']
      });
    });

    it('nên throw error khi Knowledge File không tồn tại', async () => {
      mockKnowledgeFileRepository.findOne.mockResolvedValue(null);

      await expect(
        validationHelper.validateSourceResourceStatus(
          'test-id',
          ProductCategory.KNOWLEDGE_FILE,
          {
            knowledgeFileRepository: mockKnowledgeFileRepository,
          }
        )
      ).rejects.toThrow(AppException);

      await expect(
        validationHelper.validateSourceResourceStatus(
          'test-id',
          ProductCategory.KNOWLEDGE_FILE,
          {
            knowledgeFileRepository: mockKnowledgeFileRepository,
          }
        )
      ).rejects.toMatchObject({
        errorCode: MARKETPLACE_ERROR_CODES.RESOURCE_NOT_FOUND,
        message: 'Không tìm thấy Knowledge File với ID: test-id'
      });
    });

    it('nên throw error khi Knowledge File không có status APPROVED', async () => {
      mockKnowledgeFileRepository.findOne.mockResolvedValue({
        id: 'test-id',
        status: 'DRAFT'
      });

      await expect(
        validationHelper.validateSourceResourceStatus(
          'test-id',
          ProductCategory.KNOWLEDGE_FILE,
          {
            knowledgeFileRepository: mockKnowledgeFileRepository,
          }
        )
      ).rejects.toThrow(AppException);

      await expect(
        validationHelper.validateSourceResourceStatus(
          'test-id',
          ProductCategory.KNOWLEDGE_FILE,
          {
            knowledgeFileRepository: mockKnowledgeFileRepository,
          }
        )
      ).rejects.toMatchObject({
        errorCode: MARKETPLACE_ERROR_CODES.RESOURCE_NOT_APPROVED,
        message: 'Knowledge File phải có trạng thái APPROVED để có thể tạo sản phẩm. Trạng thái hiện tại: DRAFT'
      });
    });

    it('nên pass khi Agent có status APPROVED', async () => {
      mockAgentRepository.findOne.mockResolvedValue({
        id: 'test-id',
        status: 'APPROVED'
      });

      await expect(
        validationHelper.validateSourceResourceStatus(
          'test-id',
          ProductCategory.AGENT,
          {
            agentRepository: mockAgentRepository,
          }
        )
      ).resolves.not.toThrow();
    });

    it('nên pass khi Fine Tune Data có status APPROVED (user)', async () => {
      mockUserDataFineTuneRepository.findOne.mockResolvedValue({
        id: 'test-id',
        status: 'APPROVED'
      });
      mockAdminDataFineTuneRepository.findOne.mockResolvedValue(null);

      await expect(
        validationHelper.validateSourceResourceStatus(
          'test-id',
          ProductCategory.FINETUNE,
          {
            userDataFineTuneRepository: mockUserDataFineTuneRepository,
            adminDataFineTuneRepository: mockAdminDataFineTuneRepository,
          }
        )
      ).resolves.not.toThrow();
    });

    it('nên pass khi Fine Tune Data có status APPROVED (admin)', async () => {
      mockUserDataFineTuneRepository.findOne.mockResolvedValue(null);
      mockAdminDataFineTuneRepository.findOne.mockResolvedValue({
        id: 'test-id',
        status: 'APPROVED'
      });

      await expect(
        validationHelper.validateSourceResourceStatus(
          'test-id',
          ProductCategory.FINETUNE,
          {
            userDataFineTuneRepository: mockUserDataFineTuneRepository,
            adminDataFineTuneRepository: mockAdminDataFineTuneRepository,
          }
        )
      ).resolves.not.toThrow();
    });

    it('nên pass khi Strategy có status APPROVED', async () => {
      mockStrategyRepository.findOne.mockResolvedValue({
        id: 'test-id',
        status: 'APPROVED'
      });

      await expect(
        validationHelper.validateSourceResourceStatus(
          'test-id',
          ProductCategory.STRATEGY,
          {
            strategyRepository: mockStrategyRepository,
          }
        )
      ).resolves.not.toThrow();
    });

    it('nên throw error khi category không được hỗ trợ', async () => {
      await expect(
        validationHelper.validateSourceResourceStatus(
          'test-id',
          'INVALID_CATEGORY' as ProductCategory,
          {}
        )
      ).rejects.toThrow(AppException);

      await expect(
        validationHelper.validateSourceResourceStatus(
          'test-id',
          'INVALID_CATEGORY' as ProductCategory,
          {}
        )
      ).rejects.toMatchObject({
        errorCode: MARKETPLACE_ERROR_CODES.INVALID_CATEGORY,
        message: 'Loại sản phẩm không được hỗ trợ: INVALID_CATEGORY'
      });
    });

    it('nên throw error khi repository không được cung cấp', async () => {
      await expect(
        validationHelper.validateSourceResourceStatus(
          'test-id',
          ProductCategory.KNOWLEDGE_FILE,
          {}
        )
      ).rejects.toThrow(AppException);

      await expect(
        validationHelper.validateSourceResourceStatus(
          'test-id',
          ProductCategory.KNOWLEDGE_FILE,
          {}
        )
      ).rejects.toMatchObject({
        errorCode: MARKETPLACE_ERROR_CODES.GENERAL_ERROR,
        message: 'Repository không được cung cấp để kiểm tra Knowledge File'
      });
    });
  });
});
