import { Injectable, Logger } from '@nestjs/common';
import {
  KnowledgeFileRepository,
  VectorStoreRepository,
  VectorStoreFileRepository,
} from '../../repositories';
import {
  BatchCreateFilesDto,
  BatchCreateFilesResponseDto,
  FileResponseDto,
  QueryFileDto,
} from '../dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { plainToInstance } from 'class-transformer';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import { OpenAiService } from '@shared/services/ai/openai.service';
import { AppException } from '@common/exceptions/app.exception';
import { KNOWLEDGE_FILE_ERROR_CODES } from '../exceptions';
import { extname } from 'path';

import {
  CategoryFolderEnum,
  FileSizeEnum,
  TimeIntervalEnum,
} from '@shared/utils';
import { generateS3Key } from '@shared/utils/generators';
import {
  FileType
} from '@shared/utils/file/file-media-type.util';
import { OwnerType } from '@/modules/email/enum/owner-type.enum';
import {
  KnowledgeFile,
  VectorStore,
} from '@modules/data/knowledge-files/entities';
import { KnowledgeFileStatus } from '@modules/data/knowledge-files/enums/knowledge-file-status.enum';
import { Transactional } from 'typeorm-transactional';
import { KnowledgeFileUserValidationHelper } from '../helpers';

@Injectable()
export class KnowledgeFileUserService {
  private readonly logger = new Logger(KnowledgeFileUserService.name);

  constructor(
    private readonly knowledgeFileRepository: KnowledgeFileRepository,
    private readonly vectorStoreRepository: VectorStoreRepository,
    private readonly vectorStoreFileRepository: VectorStoreFileRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly openAiService: OpenAiService,
    private readonly validationHelper: KnowledgeFileUserValidationHelper,
  ) {}

  /**
   * Tạo nhiều file tri thức mới
   * @param dto Thông tin các file cần tạo
   * @param userId ID của người dùng
   * @returns Thông tin về việc tạo file thành công và URL tải lên
   */
  @Transactional()
  async batchCreateFiles(
    dto: BatchCreateFilesDto,
    userId: number,
  ): Promise<BatchCreateFilesResponseDto> {
    try {
      // Tạo một mảng để lưu trữ thông tin về các file đã tạo
      const fileCreationResults = await Promise.all(
        dto.files.map(async (file) => {
          // Xác định loại file dựa trên MIME type
          const fileType = FileType.getMimeType(file.mime);

          // Tạo S3 key cho file với tên file và thư mục theo thời gian
          // Sử dụng tên file gốc để lấy phần mở rộng, nhưng không sử dụng tên file trong key
          // Điều này giúp tránh các vấn đề với ký tự đặc biệt và tiếng Việt
          const s3Key = generateS3Key({
            baseFolder: 'knowledge',
            categoryFolder: CategoryFolderEnum.DOCUMENT,
            fileName: file.name,
            useTimeFolder: true,
          });

          // Chuẩn bị dữ liệu file
          const fileData = {
            name: file.name,
            storageKey: s3Key,
            ownerType: OwnerType.USER,
            ownedBy: userId,
            isOwner: true,
            isForSale: false,
            storage: file.storage,
            status: KnowledgeFileStatus.DRAFT,
            fileId: '',
          };

          // Sử dụng repository để tạo và lưu file
          const savedFile = await this.knowledgeFileRepository.createAndSaveKnowledgeFile(fileData);

          // Tạo presigned URL để client có thể upload file
          const uploadUrl = await this.s3Service.createPresignedWithID(
            s3Key,
            TimeIntervalEnum.TEN_MINUTES,
            fileType,
            FileSizeEnum.TWENTY_MB,
          );

          // Trả về thông tin đầy đủ về file đã tạo và URL tải lên
          return {
            id: savedFile.id,
            name: savedFile.name,
            uploadUrl,
            storageKey: s3Key,
          };
        }),
      );

      // Chuyển đổi kết quả thành DTO
      return plainToInstance(
        BatchCreateFilesResponseDto,
        { files: fileCreationResults },
        { excludeExtraneousValues: true },
      );
    } catch (error) {
      this.logger.error(
        `Error creating batch files: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_CREATE_ERROR,
        `Lỗi khi tạo batch files: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách file tri thức của người dùng
   * @param queryDto Tham số truy vấn
   * @param userId ID của người dùng
   * @returns Danh sách file tri thức với phân trang
   */
  async getFiles(
    queryDto: QueryFileDto,
    userId: number,
  ): Promise<PaginatedResult<FileResponseDto>> {
    try {
      const { vectorStoreId, page = 1, limit = 10 } = queryDto;

      // Lọc theo vector store nếu có
      let files: KnowledgeFile[] = [];
      let vectorStore: VectorStore | null = null;
      let fileIds: string[] | undefined = undefined;

      if (vectorStoreId) {
        try {
          // Kiểm tra xem vector store có tồn tại không
          vectorStore = await this.vectorStoreRepository.findOneByIdAndUserId(
            vectorStoreId,
            userId,
          );
          this.validationHelper.validateVectorStoreExists(
            vectorStore,
            vectorStoreId,
          );

          // Lấy danh sách file ID trong vector store
          const vectorStoreFiles = await this.vectorStoreFileRepository.find({
            where: { vectorStoreId },
          });

          if (vectorStoreFiles.length > 0) {
            fileIds = vectorStoreFiles.map((vsf) => vsf.fileId);
          } else {
            // Nếu không có file nào trong vector store, trả về mảng rỗng
            return {
              items: [],
              meta: {
                totalItems: 0,
                itemCount: 0,
                itemsPerPage: limit,
                totalPages: 0,
                currentPage: page,
              },
            };
          }
        } catch (error) {
          if (error instanceof AppException) {
            this.logger.warn(`Vector store không tồn tại: ${error.message}`);
            return {
              items: [],
              meta: {
                totalItems: 0,
                itemCount: 0,
                itemsPerPage: limit,
                totalPages: 0,
                currentPage: page,
              },
            };
          }
          throw error;
        }
      }

      // Sử dụng repository để lấy danh sách file với các bộ lọc
      const result = await this.knowledgeFileRepository.findAllWithPaginationAndFilters(
        queryDto,
        userId,
        KnowledgeFileStatus.DELETED,
        fileIds
      );

      files = result.items;
      const meta = result.meta;

      // Lấy thông tin vector store nếu có
      const vectorStoreName = vectorStore?.name || 'Vector Store';

      // Tạo URL xem file
      const fileResponses = await Promise.all(
        files.map(async (file) => {
          let viewUrl = '';
          try {
            // Sử dụng CdnService để tạo URL có chữ ký
            const generatedUrl = this.cdnService.generateUrlView(
              file.storageKey,
              TimeIntervalEnum.ONE_HOUR
            );

            if (!generatedUrl) {
              throw new Error('Failed to generate CDN URL');
            }

            viewUrl = generatedUrl; // Gán giá trị sau khi đã kiểm tra null
            this.logger.log(`Created view URL for file ${file.id}: ${viewUrl}`);
          } catch (error) {
            this.logger.error(
              `Failed to create view URL for file ${file.id}: ${error.message}`,
              error.stack,
            );
            viewUrl = '#'; // URL mặc định nếu có lỗi
          }

          // Lấy phần mở rộng của file từ tên file
          const extension = extname(file.name).substring(1);

          // Kiểm tra xem file có thuộc về vector store nào không
          let fileVectorStoreId: string | undefined = undefined;
          let fileVectorStoreName: string | undefined = undefined;

          try {
            if (vectorStoreId) {
              fileVectorStoreId = vectorStoreId;
              fileVectorStoreName = vectorStoreName || 'Vector Store';
            } else {
              const vectorStoreInfo =
                await this.vectorStoreFileRepository.findVectorStoreByFileId(
                  file.id,
                );

              if (vectorStoreInfo) {
                fileVectorStoreId = vectorStoreInfo.vectorStoreId;
                fileVectorStoreName =
                  vectorStoreInfo.vectorStoreName || 'Vector Store';
              }
            }
          } catch (error) {
            this.logger.warn(
              `Error getting vector store info for file ${file.id}: ${error.message}`,
            );
          }

          // Đảm bảo các trường dữ liệu đúng định dạng
          const fileResponse = {
            id: file.id,
            name: file.name,
            extension: extension || 'unknown',
            storage: Number(file.storage) || 1024,
            vectorStoreId: fileVectorStoreId,
            vectorStoreName: fileVectorStoreName,
            viewUrl: viewUrl || '#', // Đảm bảo luôn có URL
            createdAt: Number(file.createdAt) || Date.now(), // Đảm bảo createdAt là số
            updatedAt: Number(file.createdAt) || Date.now(), // Sử dụng createdAt thay cho updatedAt vì cột updated_at chưa tồn tại
          };

          // Chuyển đổi sang DTO
          return plainToInstance(FileResponseDto, fileResponse, {
            excludeExtraneousValues: true,
          });
        }),
      );

      // Trả về kết quả phân trang theo đúng cấu trúc mong muốn
      return {
        items: fileResponses,
        meta,
      };
    } catch (error) {
      this.logger.error(`Error getting files: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_LIST_ERROR,
        `Lỗi khi lấy danh sách file: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều file tri thức
   * @param fileIds Danh sách ID của các file cần xóa
   * @param userId ID của người dùng
   * @returns Thông tin về việc xóa file thành công
   */
  @Transactional()
  async deleteFiles(
    fileIds: string | string[],
    userId: number,
  ): Promise<{ success: boolean; deletedCount: number; failedItems?: { id: string; reason: string }[] }> {
    try {
      // Chuyển đổi tham số thành mảng để xử lý thống nhất
      const fileIdArray = Array.isArray(fileIds) ? fileIds : [fileIds];

      // Kiểm tra dữ liệu đầu vào
      if (!fileIdArray.length) {
        throw new AppException(
          KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_NOT_FOUND,
          'Danh sách file không được để trống',
        );
      }

      this.logger.log(`Bắt đầu xóa ${fileIdArray.length} file cho người dùng ${userId}`);

      // Tìm tất cả các file thuộc về người dùng
      const files = await this.knowledgeFileRepository.findByIdsAndUserId(fileIdArray, userId);

      // Xác thực các file tồn tại
      this.validationHelper.validateFilesExist(files, fileIdArray);

      // Theo dõi các file đã xóa thành công và thất bại
      const failedItems: { id: string; reason: string }[] = [];
      const successfullyDeletedFiles: KnowledgeFile[] = [];

      // Xử lý từng file
      for (const file of files) {
        try {
          // Xác thực file không ở trạng thái đã xóa
          if (file.status === KnowledgeFileStatus.DELETED) {
            failedItems.push({
              id: file.id,
              reason: 'File đã bị xóa trước đó',
            });
            continue;
          }

          // Xóa liên kết với vector store nếu có
          try {
            await this.vectorStoreFileRepository.delete({ fileId: file.id });
            this.logger.log(`Đã xóa liên kết vector store cho file: ${file.id}`);
          } catch (error) {
            this.logger.warn(
              `Lỗi khi xóa liên kết vector store: ${error.message}`,
              error.stack,
            );
          }

          // Xóa file từ OpenAI nếu có fileId
          if (file.fileId) {
            try {
              await this.openAiService.deleteOpenAIFile(file.fileId);
              this.logger.log(`Đã xóa file từ OpenAI: ${file.fileId}`);
            } catch (error) {
              this.logger.warn(
                `Lỗi khi xóa file từ OpenAI: ${error.message}`,
                error.stack,
              );
            }
          }

          // Xóa file từ S3
          try {
            await this.s3Service.deleteFile(file.storageKey);
            this.logger.log(`Đã xóa file từ S3: ${file.storageKey}`);
          } catch (error) {
            this.logger.warn(
              `Lỗi khi xóa file từ S3: ${error.message}`,
              error.stack,
            );
            // Tiếp tục xóa file từ database ngay cả khi xóa từ S3 thất bại
          }

          // Đánh dấu file đã xóa thành công
          successfullyDeletedFiles.push(file);
        } catch (error) {
          this.logger.error(`Lỗi khi xóa file ${file.id}: ${error.message}`, error.stack);
          failedItems.push({
            id: file.id,
            reason: error instanceof AppException ? error.message : `Lỗi khi xóa file: ${error.message}`,
          });
        }
      }

      // Xóa mềm tất cả các file đã xử lý thành công
      if (successfullyDeletedFiles.length > 0) {
        const fileIdsToDelete = successfullyDeletedFiles.map(file => file.id);
        for (const fileId of fileIdsToDelete) {
          await this.knowledgeFileRepository.softDeleteKnowledgeFile(fileId);
        }
        this.logger.log(`Đã xóa mềm ${successfullyDeletedFiles.length} file`);
      }

      return {
        success: successfullyDeletedFiles.length > 0,
        deletedCount: successfullyDeletedFiles.length,
        failedItems: failedItems.length > 0 ? failedItems : undefined,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi xóa các file: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_DELETE_ERROR,
        `Lỗi khi xóa các file: ${error.message}`,
      );
    }
  }



  /**
   * Gửi file tri thức để duyệt (chuyển trạng thái từ DRAFT sang PENDING)
   * @param fileId ID của file cần gửi duyệt
   * @param userId ID của người dùng
   * @returns Thông tin về việc gửi duyệt thành công
   */
  @Transactional()
  async submitForApproval(
    fileId: string,
    userId: number,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Tìm file trong database
      const file = await this.knowledgeFileRepository.findOneByIdAndUserId(
        fileId,
        userId,
      );

      // Xác thực file tồn tại
      this.validationHelper.validateFileExists(file, fileId);

      // Xác thực file không ở trạng thái đã xóa
      this.validationHelper.validateFileNotDeleted(file);

      // Xác thực file đang ở trạng thái DRAFT
      this.validationHelper.validateFileIsDraft(file);

      // Cập nhật trạng thái file thành PENDING
      file.status = KnowledgeFileStatus.PENDING;

      // Lưu thay đổi vào database
      await this.knowledgeFileRepository.save(file);

      this.logger.log(`File ${fileId} đã được gửi duyệt bởi người dùng ${userId}`);

      return {
        success: true,
        message: 'File đã được gửi duyệt thành công',
      };
    } catch (error) {
      this.logger.error(`Error submitting file for approval: ${error.message}`, error.stack);

      // Nếu là AppException thì ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác thì wrap lại
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_STATUS_CHANGE_ERROR,
        `Lỗi khi gửi file để duyệt: ${error.message}`,
      );
    }
  }

}
