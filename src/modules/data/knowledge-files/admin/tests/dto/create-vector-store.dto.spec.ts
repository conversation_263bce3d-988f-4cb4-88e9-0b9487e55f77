import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { CreateVectorStoreDto } from '../../dto/create-vector-store.dto';

describe('CreateVectorStoreDto', () => {
  it('nên xác thực DTO hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(CreateVectorStoreDto, {
      name: 'Test Vector Store'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi name không được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(CreateVectorStoreDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi name là chuỗi rỗng', async () => {
    // Arrange
    const dto = plainToInstance(CreateVectorStoreDto, {
      name: ''
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi name không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(CreateVectorStoreDto, {
      name: 123
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi name vượt quá độ dài tối đa', async () => {
    // Arrange
    const longName = 'A'.repeat(256);
    const dto = plainToInstance(CreateVectorStoreDto, {
      name: longName
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('maxLength');
  });

  it('nên xác thực với tên đúng bằng độ dài tối đa', async () => {
    // Arrange
    const maxLengthName = 'A'.repeat(255);
    const dto = plainToInstance(CreateVectorStoreDto, {
      name: maxLengthName
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });
});
