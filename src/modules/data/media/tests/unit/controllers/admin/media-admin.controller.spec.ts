import { Test, TestingModule } from '@nestjs/testing';
import { MediaAdminController } from '../../../../admin/controllers/media-admin.controller';
import { MediaAdminService } from '../../../../admin/services/media-admin.service';

describe('MediaAdminController', () => {
  let controller: MediaAdminController;
  let service: MediaAdminService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MediaAdminController],
      providers: [
        {
          provide: MediaAdminService,
          useValue: {
            // Mock các phương thức của service khi cần
          },
        },
      ],
    }).compile();

    controller = module.get<MediaAdminController>(MediaAdminController);
    service = module.get<MediaAdminService>(MediaAdminService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  // Thêm các test case cho các phư<PERSON>ng thức của MediaAdminController khi cần
  // <PERSON>ện tại MediaAdminController chưa có phư<PERSON>ng thức nào được triển khai
});
