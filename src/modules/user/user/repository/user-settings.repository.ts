import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserSettings } from '@modules/user/entities/user-settings.entity';

/**
 * Repository cho UserSettings
 */
@Injectable()
export class UserSettingsRepository {
  constructor(
    @InjectRepository(UserSettings)
    private readonly repository: Repository<UserSettings>,
  ) {}

  /**
   * Tìm user settings theo user ID
   * @param userId ID của người dùng
   * @returns User settings hoặc null nếu không tìm thấy
   */
  async findByUserId(userId: number): Promise<UserSettings | null> {
    return this.repository.findOne({
      where: { userId },
    });
  }

  /**
   * Tạo user settings mới
   * @param userId ID của người dùng
   * @param theme Theme settings
   * @param timezone Timezone
   * @returns User settings đã được tạo
   */
  async create(
    userId: number,
    theme?: Record<string, any>,
    timezone?: string,
  ): Promise<UserSettings> {
    const userSettings = this.repository.create({
      userId,
      theme: theme || { mode: 'light' },
      timezone: timezone || 'UTC',
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return this.repository.save(userSettings);
  }

  /**
   * Cập nhật user settings
   * @param id ID của user settings
   * @param updateData Dữ liệu cập nhật
   * @returns User settings đã được cập nhật hoặc null nếu không tìm thấy
   */
  async update(
    id: number,
    updateData: Partial<Pick<UserSettings, 'theme' | 'timezone'>>,
  ): Promise<UserSettings | null> {
    await this.repository.update(id, {
      ...updateData,
      updatedAt: Date.now(),
    });

    return this.repository.findOne({ where: { id } });
  }

  /**
   * Xóa user settings theo user ID
   * @param userId ID của người dùng
   * @returns Kết quả xóa
   */
  async deleteByUserId(userId: number): Promise<void> {
    await this.repository.delete({ userId });
  }
}
