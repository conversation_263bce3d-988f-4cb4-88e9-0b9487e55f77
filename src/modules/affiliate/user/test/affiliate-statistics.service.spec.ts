import { Test, TestingModule } from '@nestjs/testing';
import { AffiliateStatisticsService } from '../services/affiliate-statistics.service';
import { AffiliateAccountRepository } from '@modules/affiliate/repositories/affiliate-account.repository';
import { AffiliateClickRepository } from '@modules/affiliate/repositories/affiliate-click.repository';
import { AffiliateCustomerOrderRepository } from '@modules/affiliate/repositories/affiliate-customer-order.repository';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { AffiliateStatisticsQueryDto } from '../dto';
import { AppException, ErrorCode } from '@common/exceptions';

describe('AffiliateStatisticsService', () => {
  let service: AffiliateStatisticsService;
  let affiliateAccountRepository: AffiliateAccountRepository;
  let affiliateClickRepository: AffiliateClickRepository;
  let affiliateCustomerOrderRepository: AffiliateCustomerOrderRepository;
  let userRepository: UserRepository;

  // Mock repositories
  const mockAffiliateAccountRepository = {
    findByUserId: jest.fn()
  };

  const mockAffiliateClickRepository = {
    countByAffiliateAccountIdAndTimeRange: jest.fn()
  };

  const mockAffiliateCustomerOrderRepository = {
    countByAffiliateAccountIdAndTimeRange: jest.fn(),
    calculateRevenueByAffiliateAccountIdAndTimeRange: jest.fn()
  };

  const mockUserRepository = {
    countByAffiliateAccountIdAndTimeRange: jest.fn()
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AffiliateStatisticsService,
        { provide: AffiliateAccountRepository, useValue: mockAffiliateAccountRepository },
        { provide: AffiliateClickRepository, useValue: mockAffiliateClickRepository },
        { provide: AffiliateCustomerOrderRepository, useValue: mockAffiliateCustomerOrderRepository },
        { provide: UserRepository, useValue: mockUserRepository }
      ]
    }).compile();

    service = module.get<AffiliateStatisticsService>(AffiliateStatisticsService);
    affiliateAccountRepository = module.get<AffiliateAccountRepository>(AffiliateAccountRepository);
    affiliateClickRepository = module.get<AffiliateClickRepository>(AffiliateClickRepository);
    affiliateCustomerOrderRepository = module.get<AffiliateCustomerOrderRepository>(AffiliateCustomerOrderRepository);
    userRepository = module.get<UserRepository>(UserRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getStatistics', () => {
    it('should return statistics when affiliate account exists', async () => {
      // Arrange
      const userId = 1;
      const queryDto = new AffiliateStatisticsQueryDto();
      queryDto.begin = **********;
      queryDto.end = **********;

      const mockAffiliateAccount = {
        id: 123,
        availableBalance: 1500000
      };

      mockAffiliateAccountRepository.findByUserId.mockResolvedValue(mockAffiliateAccount);
      mockAffiliateClickRepository.countByAffiliateAccountIdAndTimeRange.mockResolvedValue(1250);
      mockUserRepository.countByAffiliateAccountIdAndTimeRange.mockResolvedValue(45);
      mockAffiliateCustomerOrderRepository.countByAffiliateAccountIdAndTimeRange.mockResolvedValue(78);
      mockAffiliateCustomerOrderRepository.calculateRevenueByAffiliateAccountIdAndTimeRange.mockResolvedValue(7800000);

      // Act
      const result = await service.getStatistics(userId, queryDto);

      // Assert
      expect(affiliateAccountRepository.findByUserId).toHaveBeenCalledWith(userId);
      expect(affiliateClickRepository.countByAffiliateAccountIdAndTimeRange).toHaveBeenCalledWith(
        mockAffiliateAccount.id,
        queryDto.begin,
        queryDto.end
      );
      expect(userRepository.countByAffiliateAccountIdAndTimeRange).toHaveBeenCalledWith(
        mockAffiliateAccount.id,
        queryDto.begin,
        queryDto.end
      );
      expect(affiliateCustomerOrderRepository.countByAffiliateAccountIdAndTimeRange).toHaveBeenCalledWith(
        mockAffiliateAccount.id,
        queryDto.begin,
        queryDto.end
      );
      expect(affiliateCustomerOrderRepository.calculateRevenueByAffiliateAccountIdAndTimeRange).toHaveBeenCalledWith(
        mockAffiliateAccount.id,
        queryDto.begin,
        queryDto.end
      );

      expect(result).toEqual({
        walletBalance: 1500000,
        pendingAmount: 0, // Mock value
        clickCount: 1250,
        customerCount: 45,
        orderCount: 78,
        revenue: 7800000,
        conversionRate: 6.24, // 78 / 1250 * 100
        period: {
          begin: **********,
          end: **********
        }
      });
    });

    it('should throw an exception when affiliate account does not exist', async () => {
      // Arrange
      const userId = 1;
      const queryDto = new AffiliateStatisticsQueryDto();
      mockAffiliateAccountRepository.findByUserId.mockResolvedValue(null);

      // Act & Assert
      await expect(service.getStatistics(userId, queryDto)).rejects.toThrow(
        new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy tài khoản affiliate')
      );
    });

    it('should use default time range when not provided', async () => {
      // Arrange
      const userId = 1;
      const queryDto = new AffiliateStatisticsQueryDto();
      // No begin and end provided

      const mockAffiliateAccount = {
        id: 123,
        availableBalance: 1500000
      };

      mockAffiliateAccountRepository.findByUserId.mockResolvedValue(mockAffiliateAccount);
      mockAffiliateClickRepository.countByAffiliateAccountIdAndTimeRange.mockResolvedValue(1250);
      mockUserRepository.countByAffiliateAccountIdAndTimeRange.mockResolvedValue(45);
      mockAffiliateCustomerOrderRepository.countByAffiliateAccountIdAndTimeRange.mockResolvedValue(78);
      mockAffiliateCustomerOrderRepository.calculateRevenueByAffiliateAccountIdAndTimeRange.mockResolvedValue(7800000);

      // Mock Date.now() to return a fixed timestamp
      const originalDateNow = Date.now;
      const mockTimestamp = *************; // 2023-02-01 in milliseconds
      global.Date.now = jest.fn(() => mockTimestamp);

      // Act
      const result = await service.getStatistics(userId, queryDto);

      // Assert
      const expectedBegin = Math.floor((mockTimestamp - 30 * 24 * 60 * 60 * 1000) / 1000); // 30 days before
      const expectedEnd = Math.floor(mockTimestamp / 1000);

      expect(affiliateClickRepository.countByAffiliateAccountIdAndTimeRange).toHaveBeenCalledWith(
        mockAffiliateAccount.id,
        expectedBegin,
        expectedEnd
      );

      expect(result.period).toEqual({
        begin: expectedBegin,
        end: expectedEnd
      });

      // Restore original Date.now
      global.Date.now = originalDateNow;
    });
  });
});
