import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserDataFineTune } from '../entities/user-data-fine-tune.entity';
import { PaginatedResult } from '@common/response';
import { UserDataFineTuneQueryDto } from '../user/dto/user-data-fine-tune/user-data-fine-tune-query.dto';

/**
 * Repository cho UserDataFineTune
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến user data fine-tune
 */
@Injectable()
export class UserDataFineTuneRepository extends Repository<UserDataFineTune> {
  private readonly logger = new Logger(UserDataFineTuneRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserDataFineTune, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho UserDataFineTune
   * @returns SelectQueryBuilder cho UserDataFineTune
   */
  private createBaseQuery(): SelectQueryBuilder<UserDataFineTune> {
    return this.createQueryBuilder('userDataFineTune');
  }

  /**
   * Tìm danh sách user data fine-tune với phân trang
   * @param queryDto Query parameters
   * @param userId ID của user
   * @returns Kết quả phân trang
   */
  async findWithPagination(queryDto: UserDataFineTuneQueryDto, userId: number): Promise<PaginatedResult<UserDataFineTune>> {
    const query = this.createBaseQuery()
      .select([
        'userDataFineTune.id',
        'userDataFineTune.name',
        'userDataFineTune.description',
        'userDataFineTune.status',
        'userDataFineTune.createdAt'
      ])
      .where('userDataFineTune.userId = :userId', { userId })
      .andWhere('userDataFineTune.deletedAt IS NULL');

    // Lọc theo trạng thái nếu có
    if (queryDto.status) {
      query.andWhere('userDataFineTune.status = :status', { status: queryDto.status });
    }

    // Tìm kiếm theo tên nếu có
    if (queryDto.search) {
      query.andWhere('userDataFineTune.name ILIKE :search', {
        search: `%${queryDto.search}%`
      });
    }

    // Sắp xếp
    const sortBy = queryDto.sortBy || 'createdAt';
    const sortDirection = queryDto.sortDirection || 'DESC';
    query.orderBy(`userDataFineTune.${sortBy}`, sortDirection);

    // Phân trang
    const skip = (queryDto.page - 1) * queryDto.limit;
    query.skip(skip).take(queryDto.limit);

    const [items, totalItems] = await query.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: queryDto.limit,
        totalPages: Math.ceil(totalItems / queryDto.limit),
        currentPage: queryDto.page
      }
    };
  }

  /**
   * Tìm dataset theo ID và userId để đảm bảo ownership
   * @param id ID của dataset
   * @param userId ID của user
   * @returns Dataset entity hoặc null
   */
  async findByIdAndUserId(id: string, userId: number): Promise<UserDataFineTune | null> {
    return this.createBaseQuery()
      .where('userDataFineTune.id = :id', { id })
      .andWhere('userDataFineTune.userId = :userId', { userId })
      .andWhere('userDataFineTune.deletedAt IS NULL')
      .getOne();
  }

  /**
   * Soft delete dataset
   * @param id ID của dataset
   * @param userId ID của user
   * @returns True nếu xóa thành công
   */
  async softDeleteByIdAndUserId(id: string, userId: number): Promise<boolean> {
    const result = await this.createQueryBuilder()
      .update(UserDataFineTune)
      .set({
        deletedAt: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint'
      })
      .where('id = :id', { id })
      .andWhere('userId = :userId', { userId })
      .andWhere('deletedAt IS NULL')
      .execute();

    return (result.affected ?? 0) > 0;
  }

  /**
   * Kiểm tra tên dataset đã tồn tại chưa cho user
   * @param name Tên dataset
   * @param userId ID của user
   * @param excludeId ID cần loại trừ (cho update)
   * @returns True nếu đã tồn tại
   */
  async existsByName(name: string, userId: number, excludeId?: string): Promise<boolean> {
    const query = this.createQueryBuilder('userDataFineTune')
      .where('userDataFineTune.name = :name', { name })
      .andWhere('userDataFineTune.userId = :userId', { userId })
      .andWhere('userDataFineTune.deletedAt IS NULL');

    if (excludeId) {
      query.andWhere('userDataFineTune.id != :excludeId', { excludeId });
    }

    const count = await query.getCount();
    return count > 0;
  }
}

