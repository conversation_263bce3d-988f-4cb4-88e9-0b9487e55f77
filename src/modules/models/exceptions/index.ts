// Models Module Error Codes
export * from './models.exception';

// Legacy error codes (keep for backward compatibility)
import { ErrorCode } from '@common/exceptions/app.exception';
import { HttpStatus } from '@nestjs/common';

// Model Management System Exceptions (Range: 11000-11099)
export const MODEL_TRAINING_ERROR_CODES = {
  MODEL_NOT_FOUND: new ErrorCode(11000, 'Không tìm thấy model', HttpStatus.NOT_FOUND),
  INVALID_MODEL_CONFIG: new ErrorCode(11001, 'Cấu hình model không hợp lệ', HttpStatus.BAD_REQUEST),
  MODEL_ALREADY_EXISTS: new ErrorCode(11002, 'Model đã tồn tại', HttpStatus.CONFLICT),
  PROVIDER_NOT_SUPPORTED: new ErrorCode(11003, 'Nhà cung cấp không được hỗ trợ', HttpStatus.BAD_REQUEST),
  API_KEY_INVALID: new ErrorCode(11004, 'API key không hợp lệ', HttpStatus.UNAUTHORIZED),
  MODEL_TRAINING_FAILED: new ErrorCode(11005, 'Quá trình training model thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  INSUFFICIENT_PERMISSIONS: new ErrorCode(11006, 'Không đủ quyền truy cập', HttpStatus.FORBIDDEN),
  PROVIDER_NAME_EXISTS: new ErrorCode(11007, 'Tên nhà cung cấp đã tồn tại', HttpStatus.CONFLICT),
  ENCRYPTION_ERROR: new ErrorCode(11008, 'Lỗi mã hóa dữ liệu', HttpStatus.INTERNAL_SERVER_ERROR),
  PROVIDER_CREATE_FAILED: new ErrorCode(11009, 'Tạo nhà cung cấp thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  DATA_FETCH_ERROR: new ErrorCode(11010, 'Lỗi lấy dữ liệu', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_PROVIDER_NOT_FOUND: new ErrorCode(11011, 'Không tìm thấy nhà cung cấp của user', HttpStatus.NOT_FOUND),
  PROVIDER_UPDATE_FAILED: new ErrorCode(11012, 'Cập nhật nhà cung cấp thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  PROVIDER_DELETE_FAILED: new ErrorCode(11013, 'Xóa nhà cung cấp thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  API_KEY_VALIDATION_FAILED: new ErrorCode(11014, 'Xác thực API key thất bại', HttpStatus.BAD_REQUEST),
  PROVIDER_NOT_FOUND: new ErrorCode(11015, 'Không tìm thấy nhà cung cấp', HttpStatus.NOT_FOUND),
  API_KEY_EXPIRED: new ErrorCode(11016, 'API key đã hết hạn', HttpStatus.UNAUTHORIZED),
  INVALID_MODEL_DATA: new ErrorCode(11017, 'Dữ liệu model không hợp lệ', HttpStatus.BAD_REQUEST),
};