import { Column, Entity, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng model_base trong cơ sở dữ liệu
 * Định nghĩa mô hình cơ sở mà hệ thống hỗ trợ
 */
@Entity('model_base')
export class ModelBase {
  systemKeyLlmId(systemKeyLlmId: any) {
    throw new Error('Method not implemented.');
  }
  /**
   * UUID của model cơ sở
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên hiển thị của model
   */
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Tên hiển thị của model'
  })
  name: string;

  /**
   * Mô tả về model
   */
  @Column({
    type: 'text',
    nullable: true,
    comment: 'Mô tả về model'
  })
  description?: string;

  /**
   * <PERSON><PERSON><PERSON> kết đến với bảng model_registry, mô tả khả năng của mô hình
   */
  @Column({
    name: 'capabilities',
    type: 'uuid',
    comment: '<PERSON>ác khả năng của model'
  })
  capabilities: string;

  /**
   * Trạng thái hoạt động của model
   */
  @Column({
    name: 'active',
    type: 'boolean',
    default: true,
    comment: 'Trạng thái hoạt động của model'
  })
  active: boolean;

  /**
   * Thời gian tạo
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Người tạo
   */
  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number | null;

  /**
   * Thời gian cập nhật
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;

  /**
   * Người cập nhật
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * Thời gian xóa mềm
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;

  /**
   * Người thực hiện xóa
   */
  @Column({ name: 'deleted_by', type: 'bigint', nullable: true })
  deletedBy: number | null;
}
