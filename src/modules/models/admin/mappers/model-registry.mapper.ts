import { ModelRegistry } from '../../entities/model-registry.entity';
import { ModelRegistryResponseDto } from '../dto/model-registry';

/**
 * Mapper cho ModelRegistry
 * Chuyển đổi giữa entity và DTO
 */
export class ModelRegistryMapper {
  /**
   * Chuyển đổi entity sang response DTO
   * @param entity ModelRegistry entity
   * @param createdByName Tên người tạo (optional)
   * @param updatedByName Tên người cập nhật (optional)
   * @param deletedByName Tên người xóa (optional)
   * @returns ModelRegistryResponseDto
   */
  // static toResponseDto(
  //   entity: ModelRegistry,
  //   createdByName?: string | null,
  //   updatedByName?: string | null,
  //   deletedByName?: string | null
  // ): ModelRegistryResponseDto {
  //   return {
  //     id: entity.id,
  //     provider: entity.provider,
  //     modelNamePattern: entity.modelNamePattern,
  //     inputModalities: entity.inputModalities || [],
  //     outputModalities: entity.outputModalities || [],
  //     samplingParameters: entity.samplingParameters || {},
  //     features: entity.features || [],
  //     createdAt: entity.createdAt,
  //     createdByName,
  //     updatedAt: entity.updatedAt,
  //     updatedByName,
  //     deletedAt: entity.deletedAt,
  //     deletedBy: entity.deletedBy,
  //     deletedByName
  //   };
  // }

  /**
   * Chuyển đổi mảng entities sang mảng response DTOs
   * @param entities Mảng ModelRegistry entities
   * @param employeeNames Map ID employee -> tên (optional)
   * @returns Mảng ModelRegistryResponseDto
   */
  // static toResponseDtoArray(
  //   entities: ModelRegistry[],
  //   employeeNames?: Map<number, string>
  // ): ModelRegistryResponseDto[] {
  //   return entities.map(entity => {
  //     const createdByName = entity.createdBy && employeeNames
  //       ? employeeNames.get(entity.createdBy) || null
  //       : null;
  //     const updatedByName = entity.updatedBy && employeeNames
  //       ? employeeNames.get(entity.updatedBy) || null
  //       : null;
  //     const deletedByName = entity.deletedBy && employeeNames
  //       ? employeeNames.get(entity.deletedBy) || null
  //       : null;

  //     return this.toResponseDto(entity, createdByName, updatedByName, deletedByName);
  //   });
  // }

  /**
   * Validate model name pattern
   * @param pattern Pattern cần validate
   * @returns true nếu hợp lệ
   */
  static validatePattern(pattern: string): boolean {
    if (!pattern || pattern.trim().length === 0) {
      return false;
    }

    // Pattern không được chứa ký tự đặc biệt nguy hiểm
    const dangerousChars = /[<>\"'&]/;
    if (dangerousChars.test(pattern)) {
      return false;
    }

    // Pattern phải có ít nhất 2 ký tự
    if (pattern.trim().length < 2) {
      return false;
    }

    return true;
  }

  /**
   * Validate input modalities
   * @param modalities Mảng modalities
   * @returns true nếu hợp lệ
   */
  static validateInputModalities(modalities?: string[]): boolean {
    if (!modalities) {
      return true; // Optional field
    }

    if (!Array.isArray(modalities)) {
      return false;
    }

    const validModalities = ['text', 'image', 'audio', 'video', 'code'];
    return modalities.every(modality =>
      typeof modality === 'string' &&
      validModalities.includes(modality.toLowerCase())
    );
  }

  /**
   * Validate output modalities
   * @param modalities Mảng modalities
   * @returns true nếu hợp lệ
   */
  static validateOutputModalities(modalities?: string[]): boolean {
    if (!modalities) {
      return true; // Optional field
    }

    if (!Array.isArray(modalities)) {
      return false;
    }

    const validModalities = ['text', 'image', 'audio', 'video', 'code'];
    return modalities.every(modality =>
      typeof modality === 'string' &&
      validModalities.includes(modality.toLowerCase())
    );
  }

  /**
   * Validate sampling parameters
   * @param params Sampling parameters object
   * @returns true nếu hợp lệ
   */
  static validateSamplingParameters(params: any): boolean {
    if (!params || typeof params !== 'object') {
      return true; // Optional field
    }

    // Kiểm tra các tham số phổ biến
    const validParams = [
      'temperature', 'top_p', 'top_k', 'max_tokens',
      'frequency_penalty', 'presence_penalty', 'stop'
    ];

    for (const key of Object.keys(params)) {
      if (!validParams.includes(key)) {
        continue; // Cho phép tham số không chuẩn
      }

      const value = params[key];

      // Validate specific parameters
      switch (key) {
        case 'temperature':
          if (typeof value !== 'number' || value < 0 || value > 2) {
            return false;
          }
          break;
        case 'top_p':
          if (typeof value !== 'number' || value < 0 || value > 1) {
            return false;
          }
          break;
        case 'top_k':
          if (typeof value !== 'number' || value < 1) {
            return false;
          }
          break;
        case 'max_tokens':
          if (typeof value !== 'number' || value < 1) {
            return false;
          }
          break;
        case 'frequency_penalty':
        case 'presence_penalty':
          if (typeof value !== 'number' || value < -2 || value > 2) {
            return false;
          }
          break;
        case 'stop':
          if (!Array.isArray(value) && typeof value !== 'string') {
            return false;
          }
          break;
      }
    }

    return true;
  }

  /**
   * Validate features array
   * @param features Mảng features
   * @returns true nếu hợp lệ
   */
  static validateFeatures(features?: string[]): boolean {
    if (!features) {
      return true; // Optional field
    }

    if (!Array.isArray(features)) {
      return false;
    }

    const validFeatures = [
      'chat', 'completion', 'embedding', 'fine-tuning',
      'function-calling', 'vision', 'audio', 'reasoning',
      'code-generation', 'translation', 'summarization'
    ];

    return features.every(feature =>
      typeof feature === 'string' &&
      validFeatures.includes(feature.toLowerCase())
    );
  }

  /**
   * Test pattern matching với model name
   * @param pattern Pattern từ registry
   * @param modelName Tên model cần test
   * @returns true nếu match
   */
  static testPatternMatch(pattern: string, modelName: string): boolean {
    if (!pattern || !modelName) {
      return false;
    }

    try {
      // Convert pattern thành regex
      // Thay thế * thành .* và escape các ký tự đặc biệt khác
      const regexPattern = pattern
        .replace(/[.+?^${}()|[\]\\]/g, '\\$&') // Escape regex special chars
        .replace(/\*/g, '.*'); // Convert * to .*

      const regex = new RegExp(`^${regexPattern}$`, 'i');
      return regex.test(modelName);
    } catch (error) {
      // Nếu regex không hợp lệ, fallback về string matching
      return modelName.toLowerCase().includes(pattern.toLowerCase());
    }
  }

  /**
   * Normalize modalities array
   * @param modalities Mảng modalities
   * @returns Mảng đã normalize
   */
  static normalizeModalities(modalities?: string[]): string[] {
    if (!Array.isArray(modalities)) {
      return [];
    }

    return modalities
      .map(m => m.toLowerCase().trim())
      .filter((m, index, arr) => arr.indexOf(m) === index) // Remove duplicates
      .sort();
  }

  /**
   * Normalize features array
   * @param features Mảng features
   * @returns Mảng đã normalize
   */
  static normalizeFeatures(features?: string[]): string[] {
    if (!Array.isArray(features)) {
      return [];
    }

    return features
      .map(f => f.toLowerCase().trim())
      .filter((f, index, arr) => arr.indexOf(f) === index) // Remove duplicates
      .sort();
  }
}
