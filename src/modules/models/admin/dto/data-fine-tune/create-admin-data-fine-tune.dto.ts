import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * DTO tạo mới admin data fine-tune
 */
export class CreateAdminDataFineTuneDto {
  @ApiProperty({
    description: 'Tên bộ dữ liệu',
    example: 'Customer Service Training Dataset',
    maxLength: 255
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Mô tả về bộ dữ liệu',
    example: 'Bộ dữ liệu huấn luyện cho chatbot hỗ trợ khách hàng',
    required: false
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Dữ liệu huấn luyện',
    example: 'application/jsonl'
  })
  @IsString()
  @IsNotEmpty()
  trainDataset: string;

  @ApiProperty({
    description: 'Dữ liệu huấn luyện',
    example: 'application/jsonl'
  })
  @IsString()
  @IsNotEmpty()
  validDataset?: string;
}
