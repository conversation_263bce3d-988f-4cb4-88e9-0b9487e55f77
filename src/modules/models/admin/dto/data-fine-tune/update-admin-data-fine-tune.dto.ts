import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

/**
 * DTO cập nhật admin data fine-tune
 */
export class UpdateAdminDataFineTuneDto {
  @ApiProperty({
    description: 'Tên bộ dữ liệu',
    example: 'Customer Service Training Dataset v2',
    required: false
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Mô tả về bộ dữ liệu',
    example: 'Bộ dữ liệu huấn luyện cho chatbot hỗ trợ khách hàng - phiên bản cập nhật',
    required: false
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'S3 key của file dữ liệu huấn luyện',
    example: 'data-fine-tune/train/123e4567-e89b-12d3-a456-426614174000.jsonl',
    required: false
  })
  @IsOptional()
  @IsString()
  trainDataset?: string;

  @ApiProperty({
    description: 'S3 key của file dữ liệu validation',
    example: 'data-fine-tune/valid/123e4567-e89b-12d3-a456-426614174000.jsonl',
    required: false
  })
  @IsOptional()
  @IsString()
  validDataset?: string;
}
