import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response template email
 */
export class TemplateEmailResponseDto {
  /**
   * ID của template
   * @example 1
   */
  @ApiProperty({
    description: 'ID của template',
    example: 1,
  })
  id: number;

  /**
   * ID của user tạo template
   * @example 1
   */
  @ApiProperty({
    description: 'ID của user tạo template',
    example: 1,
  })
  userId: number;

  /**
   * Tên template
   * @example "Newsletter tháng 1"
   */
  @ApiProperty({
    description: 'Tên template',
    example: 'Newsletter tháng 1',
  })
  name: string;

  /**
   * Tiêu đề email
   * @example "🎉 Khuyến mãi đặc biệt dành cho bạn!"
   */
  @ApiProperty({
    description: 'Tiêu đề email',
    example: '🎉 Khuyến mãi đặc biệt dành cho bạn!',
  })
  subject: string;

  /**
   * Nội dung HTML (field mới)
   * @example "<!DOCTYPE html><html><body><h1>Xin chào {customer_name}!</h1></body></html>"
   */
  @ApiProperty({
    description: 'Nội dung HTML (field mới)',
    example: '<!DOCTYPE html><html><body><h1>Xin chào {customer_name}!</h1></body></html>',
  })
  htmlContent: string;

  /**
   * Nội dung HTML (field cũ - backward compatibility)
   * @example "<!DOCTYPE html><html><body><h1>Xin chào {customer_name}!</h1></body></html>"
   */
  @ApiProperty({
    description: 'Nội dung HTML (field cũ - backward compatibility)',
    example: '<!DOCTYPE html><html><body><h1>Xin chào {customer_name}!</h1></body></html>',
  })
  content: string;

  /**
   * Nội dung text thuần
   * @example "Xin chào {customer_name}! Cảm ơn bạn đã đăng ký newsletter..."
   */
  @ApiProperty({
    description: 'Nội dung text thuần',
    example: 'Xin chào {customer_name}! Cảm ơn bạn đã đăng ký newsletter...',
    required: false,
  })
  textContent?: string;

  /**
   * Loại template
   * @example "NEWSLETTER"
   */
  @ApiProperty({
    description: 'Loại template',
    example: 'NEWSLETTER',
    required: false,
  })
  type?: string;

  /**
   * Trạng thái template
   * @example "DRAFT"
   */
  @ApiProperty({
    description: 'Trạng thái template',
    example: 'DRAFT',
    enum: ['DRAFT', 'ACTIVE', 'ARCHIVED'],
  })
  status: 'DRAFT' | 'ACTIVE' | 'ARCHIVED';

  /**
   * Preview text
   * @example "Đừng bỏ lỡ ưu đãi lên đến 50% cho tất cả sản phẩm..."
   */
  @ApiProperty({
    description: 'Preview text',
    example: 'Đừng bỏ lỡ ưu đãi lên đến 50% cho tất cả sản phẩm...',
    required: false,
  })
  previewText?: string;

  /**
   * Danh sách tags
   * @example ["newsletter", "promotion", "monthly"]
   */
  @ApiProperty({
    description: 'Danh sách tags',
    example: ['newsletter', 'promotion', 'monthly'],
    type: [String],
  })
  tags: string[];

  /**
   * Danh sách placeholders (tên biến)
   * @example ["customer_name", "product_name", "discount_amount"]
   */
  @ApiProperty({
    description: 'Danh sách placeholders (tên biến)',
    example: ['customer_name', 'product_name', 'discount_amount'],
    type: [String],
  })
  placeholders: string[];

  /**
   * Metadata của biến (chi tiết)
   * @example {"customer_name": {"type": "TEXT", "defaultValue": "Khách hàng", "required": true, "description": "Tên khách hàng"}}
   */
  @ApiProperty({
    description: 'Metadata của biến (chi tiết)',
    example: {
      customer_name: {
        type: 'TEXT',
        defaultValue: 'Khách hàng',
        required: true,
        description: 'Tên khách hàng'
      }
    },
    required: false,
  })
  variableMetadata?: Record<string, {
    type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE';
    defaultValue?: string;
    required?: boolean;
    description?: string;
  }>;

  /**
   * Thời gian tạo (Unix timestamp)
   * @example 1640995200000
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1640995200000,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @example 1640995200000
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1640995200000,
  })
  updatedAt: number;
}
