import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>A<PERSON>y, IsNotEmpty, IsOptional, IsString, MaxLength, IsEnum, ValidateNested, Length } from 'class-validator';
import { Type } from 'class-transformer';
import { EmailVariableDto } from './email-variable.dto';

/**
 * DTO cho tạo template email mới
 */
export class CreateTemplateEmailDto {
  /**
   * Tên template
   * @example "Newsletter tháng 1"
   */
  @ApiProperty({
    description: 'Tên template',
    example: 'Newsletter tháng 1',
    minLength: 1,
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 255)
  name: string;

  /**
   * Tiêu đề email
   * @example "🎉 Khuyến mãi đặc biệt dành cho bạn!"
   */
  @ApiProperty({
    description: 'Tiêu đề email',
    example: '🎉 Khuyến mãi đặc biệt dành cho bạn!',
    minLength: 1,
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 255)
  subject: string;

  /**
   * Nội dung HTML của email (field mới)
   * @example "<!DOCTYPE html><html><body><h1>Xin chào {customer_name}!</h1></body></html>"
   */
  @ApiProperty({
    description: 'Nội dung HTML của email (field mới)',
    example: '<!DOCTYPE html><html><body><h1>Xin chào {customer_name}!</h1></body></html>',
    required: false,
  })
  @IsOptional()
  @IsString()
  htmlContent?: string;

  /**
   * Nội dung HTML của email (field cũ - backward compatibility)
   * @example "<!DOCTYPE html><html><body><h1>Xin chào {customer_name}!</h1></body></html>"
   */
  @ApiProperty({
    description: 'Nội dung HTML của email (field cũ - backward compatibility)',
    example: '<!DOCTYPE html><html><body><h1>Xin chào {customer_name}!</h1></body></html>',
    required: false,
  })
  @IsOptional()
  @IsString()
  content?: string;

  /**
   * Nội dung text thuần (tùy chọn)
   * @example "Xin chào {customer_name}! Cảm ơn bạn đã đăng ký newsletter..."
   */
  @ApiProperty({
    description: 'Nội dung text thuần (tùy chọn)',
    example: 'Xin chào {customer_name}! Cảm ơn bạn đã đăng ký newsletter...',
    required: false,
  })
  @IsOptional()
  @IsString()
  textContent?: string;

  /**
   * Loại template
   * @example "NEWSLETTER"
   */
  @ApiProperty({
    description: 'Loại template',
    example: 'NEWSLETTER',
    enum: ['NEWSLETTER', 'PROMOTIONAL', 'TRANSACTIONAL', 'WELCOME', 'ABANDONED_CART', 'FOLLOW_UP'],
    required: false,
  })
  @IsOptional()
  @IsEnum(['NEWSLETTER', 'PROMOTIONAL', 'TRANSACTIONAL', 'WELCOME', 'ABANDONED_CART', 'FOLLOW_UP'])
  type?: string;

  /**
   * Preview text hiển thị trong inbox
   * @example "Đừng bỏ lỡ ưu đãi lên đến 50% cho tất cả sản phẩm..."
   */
  @ApiProperty({
    description: 'Preview text hiển thị trong inbox',
    example: 'Đừng bỏ lỡ ưu đãi lên đến 50% cho tất cả sản phẩm...',
    required: false,
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  previewText?: string;

  /**
   * Danh sách tags
   * @example ["newsletter", "promotion", "monthly"]
   */
  @ApiProperty({
    description: 'Danh sách tags',
    example: ['newsletter', 'promotion', 'monthly'],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  /**
   * Danh sách biến động trong template
   * @example [{"name": "customer_name", "type": "TEXT", "defaultValue": "Khách hàng", "required": true, "description": "Tên khách hàng"}]
   */
  @ApiProperty({
    description: 'Danh sách biến động trong template',
    example: [
      {
        name: 'customer_name',
        type: 'TEXT',
        defaultValue: 'Khách hàng',
        required: true,
        description: 'Tên khách hàng'
      }
    ],
    type: [EmailVariableDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EmailVariableDto)
  variables?: EmailVariableDto[];
}
