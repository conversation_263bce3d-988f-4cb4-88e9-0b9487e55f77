import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi overview template email
 */
export class TemplateEmailOverviewResponseDto {
  /**
   * Tổng số templates
   * @example 45
   */
  @ApiProperty({
    description: 'Tổng số templates',
    example: 45,
  })
  totalTemplates: number;

  /**
   * Tổng số template hoạt động
   * @example 32
   */
  @ApiProperty({
    description: 'Tổng số template hoạt động',
    example: 32,
  })
  activeTemplates: number;

  /**
   * Tổng số template bản nháp
   * @example 13
   */
  @ApiProperty({
    description: 'Tổng số template bản nháp',
    example: 13,
  })
  draftTemplates: number;

  /**
   * Tổng số đã gửi test tuần này
   * @example 8
   */
  @ApiProperty({
    description: 'Tổng số đã gửi test tuần này',
    example: 8,
  })
  testSentThisWeek: number;

  /**
   * S<PERSON> template thêm mới tuần này
   * @example 5
   */
  @ApiProperty({
    description: '<PERSON><PERSON> template thêm mới tuần này',
    example: 5,
  })
  newTemplatesThisWeek: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1619171200,
  })
  updatedAt: number;
}
