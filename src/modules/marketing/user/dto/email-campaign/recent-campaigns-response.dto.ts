import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin một chiến dịch gần đây
 */
export class RecentCampaignDto {
  /**
   * ID của chiến dịch
   * @example 1
   */
  @ApiProperty({
    description: 'ID của chiến dịch',
    example: 1,
  })
  id: number;

  /**
   * Tên chiến dịch
   * @example "Chiến dịch khuyến mãi mùa hè 2024"
   */
  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Chiến dịch khuyến mãi mùa hè 2024',
  })
  name: string;

  /**
   * Tổng số người nhận
   * @example 1500
   */
  @ApiProperty({
    description: 'Tổng số người nhận',
    example: 1500,
  })
  totalRecipients: number;

  /**
   * Trạng thái chiến dịch
   * @example "SENT"
   */
  @ApiProperty({
    description: 'Trạng thái chiến dịch',
    example: 'SENT',
    enum: ['DRAFT', 'SCHEDULED', 'SENDING', 'SENT', 'FAILED', 'CANCELLED'],
  })
  status: string;

  /**
   * Thời gian chạy chiến dịch (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian chạy chiến dịch (Unix timestamp)',
    example: 1619171200,
  })
  runAt: number;

  /**
   * Tỷ lệ tin nhắn đã gửi (%)
   * @example 95.5
   */
  @ApiProperty({
    description: 'Tỷ lệ tin nhắn đã gửi (%)',
    example: 95.5,
    required: false,
  })
  sentRate?: number;

  /**
   * Tỷ lệ mở email (%)
   * @example 25.8
   */
  @ApiProperty({
    description: 'Tỷ lệ mở email (%)',
    example: 25.8,
    required: false,
  })
  openRate?: number;

  /**
   * Tỷ lệ click email (%)
   * @example 12.3
   */
  @ApiProperty({
    description: 'Tỷ lệ click email (%)',
    example: 12.3,
    required: false,
  })
  clickRate?: number;
}

/**
 * DTO cho phản hồi danh sách chiến dịch gần đây
 */
export class RecentCampaignsResponseDto {
  /**
   * Danh sách chiến dịch gần đây
   */
  @ApiProperty({
    description: 'Danh sách chiến dịch gần đây',
    type: [RecentCampaignDto],
  })
  campaigns: RecentCampaignDto[];

  /**
   * Tổng số chiến dịch
   * @example 25
   */
  @ApiProperty({
    description: 'Tổng số chiến dịch',
    example: 25,
  })
  totalCampaigns: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1619171200,
  })
  updatedAt: number;
}
