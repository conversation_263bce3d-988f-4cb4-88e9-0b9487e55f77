# API Template Email Overview

## Tổng quan

API này cung cấp thông tin tổng quan về template email của người dùng, bao gồm các thống kê quan trọng để theo dõi hiệu quả sử dụng templates.

## Endpoint

### Template Email Overview

```
GET /v1/user/marketing/template-emails/overview
```

#### Authentication
Y<PERSON>u cầu JWT token của user trong header:
```
Authorization: Bearer <jwt_token>
```

#### Response
```typescript
{
  "success": true,
  "message": "Lấy thông tin overview template email thành công",
  "result": {
    "totalTemplates": 45,           // Tổng số templates
    "activeTemplates": 32,          // Tổng số template hoạt động
    "draftTemplates": 13,           // Tổng số template bản nháp
    "testSentThisWeek": 8,          // Tổng số đã gửi test tuần này
    "newTemplatesThisWeek": 5,      // <PERSON>ố template thêm mới tuần này
    "updatedAt": 1619171200         // Thời gian cập nhật (Unix timestamp)
  }
}
```

## Cách tính toán Metrics

### 1. Tổng Templates
```sql
SELECT COUNT(*) FROM user_template_email WHERE user_id = ?
```

### 2. Template Hoạt động
```sql
SELECT COUNT(*) FROM user_template_email 
WHERE user_id = ? AND status = 'ACTIVE'
```

### 3. Template Bản nháp
```sql
SELECT COUNT(*) FROM user_template_email 
WHERE user_id = ? AND status = 'DRAFT'
```

### 4. Template mới tuần này
```sql
SELECT COUNT(*) FROM user_template_email 
WHERE user_id = ? AND created_at >= start_of_week_timestamp
```

### 5. Test gửi tuần này
Hiện tại chưa có bảng tracking test email, trả về 0.
**TODO**: Cần implement bảng tracking test emails.

## Trạng thái Template

Template email có các trạng thái sau:
- **DRAFT**: Bản nháp, chưa hoàn thiện
- **ACTIVE**: Đang hoạt động, có thể sử dụng
- **INACTIVE**: Không hoạt động, tạm ngưng sử dụng

## Tính toán thời gian tuần

API tính toán "tuần này" từ thứ 2 đến chủ nhật:
- Thứ 2 00:00:00 là điểm bắt đầu tuần
- Chủ nhật 23:59:59 là điểm kết thúc tuần

## Ví dụ sử dụng

### Frontend Integration
```typescript
const apiService = new TemplateEmailApiService(token);

// Lấy overview
const overview = await apiService.getOverview();
console.log(`Tổng templates: ${overview.totalTemplates}`);
console.log(`Templates hoạt động: ${overview.activeTemplates}`);
console.log(`Templates bản nháp: ${overview.draftTemplates}`);
console.log(`Test gửi tuần này: ${overview.testSentThisWeek}`);
console.log(`Templates mới tuần này: ${overview.newTemplatesThisWeek}`);
```

### Response Example
```json
{
  "success": true,
  "message": "Lấy thông tin overview template email thành công",
  "result": {
    "totalTemplates": 45,
    "activeTemplates": 32,
    "draftTemplates": 13,
    "testSentThisWeek": 0,
    "newTemplatesThisWeek": 5,
    "updatedAt": 1619171200
  }
}
```

## Lưu ý

1. API chỉ trả về dữ liệu của user hiện tại (dựa trên JWT token)
2. Metrics được tính toán real-time từ database
3. Thời gian được tính theo múi giờ server
4. Trường `testSentThisWeek` hiện tại trả về 0 do chưa có tracking test emails

## Roadmap

### Tính năng cần phát triển:
1. **Test Email Tracking**: Tạo bảng lưu trữ lịch sử gửi test email
2. **Template Usage Analytics**: Theo dõi số lần sử dụng template trong campaigns
3. **Performance Metrics**: Tỷ lệ mở và click của từng template
4. **Template Categories**: Phân loại template theo danh mục
