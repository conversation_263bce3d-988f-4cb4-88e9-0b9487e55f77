import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin về tool
 */
export class ToolResponseDto {
  /**
   * ID của tool
   */
  @ApiProperty({
    description: 'ID của tool',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  /**
   * Tên tool
   */
  @ApiProperty({
    description: 'Tên tool',
    example: 'Web Search',
  })
  name: string;

  /**
   * Mô tả tool
   */
  @ApiProperty({
    description: 'Mô tả tool',
    example: 'Tìm kiếm thông tin trên web',
  })
  description: string;

  /**
   * Loại tool
   */
  @ApiProperty({
    description: 'Loại tool',
    example: 'ADMIN',
  })
  type: string;

  /**
   * ID phiên bản của tool (nếu có)
   */
  @ApiProperty({
    description: 'ID phiên bản của tool',
    example: '550e8400-e29b-41d4-a716-446655440000',
    required: false,
  })
  versionId?: string;

  /**
   * <PERSON><PERSON> phiên bản của tool (nếu có)
   */
  @ApiProperty({
    description: 'Số phiên bản của tool',
    example: 'V.1.0.0',
    required: false,
  })
  versionNumber?: string;

  /**
   * Tên hàm của tool (nếu có)
   */
  @ApiProperty({
    description: 'Tên hàm của tool',
    example: 'webSearch',
    required: false,
  })
  toolName?: string;

  /**
   * Tham số của tool (nếu có)
   */
  @ApiProperty({
    description: 'Tham số của tool',
    example: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'Câu truy vấn tìm kiếm',
        },
      },
      required: ['query'],
    },
    required: false,
  })
  parameters?: any;

  /**
   * Endpoint của tool (nếu là user tool)
   */
  @ApiProperty({
    description: 'Endpoint của tool',
    example: '/api/v1/search',
    required: false,
  })
  endpoint?: string;

  /**
   * Phương thức HTTP của tool (nếu là user tool)
   */
  @ApiProperty({
    description: 'Phương thức HTTP của tool',
    example: 'GET',
    required: false,
  })
  method?: string;

  /**
   * Thông tin tích hợp của tool (nếu là tool tích hợp)
   */
  @ApiProperty({
    description: 'Thông tin tích hợp của tool',
    required: false,
  })
  integration?: {
    baseUrl: string;
    endpoint: string;
    method: string;
    hasAuth: boolean;
  };

  /**
   * Thông tin API key (nếu là tool tích hợp có API key)
   */
  @ApiProperty({
    description: 'Thông tin API key',
    required: false,
  })
  apiKeyInfo?: {
    paramName: string;
    apiKey: string;
    schemeName?: string;
  };
}



/**
 * DTO cho response lấy danh sách tool của agent (đã loại bỏ group tool)
 */
export class AgentToolsResponseDto {
  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  agentId: string;

  /**
   * Tên của agent
   */
  @ApiProperty({
    description: 'Tên của agent',
    example: 'Marketing Agent',
  })
  agentName: string;

  /**
   * Danh sách tool (đã loại bỏ group)
   */
  @ApiProperty({
    description: 'Danh sách tool',
    type: [ToolResponseDto],
  })
  tools: ToolResponseDto[];

  /**
   * Tổng số tool
   */
  @ApiProperty({
    description: 'Tổng số tool',
    example: 10,
  })
  totalTools: number;
}
