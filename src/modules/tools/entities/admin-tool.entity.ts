import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ToolStatusEnum } from '../constants/tool-status.enum';
import { AccessTypeEnum } from '../constants/access-type.enum';

/**
 * Entity đại diện cho bảng admin_tools trong cơ sở dữ liệu
 * Bảng lưu trữ thông tin cơ bản của các tool do admin tạo
 */
@Entity('admin_tools')
export class AdminTool {
  /**
   * ID duy nhất của tool, sử dụng UUID
   */
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  /**
   * Tên của tool, dùng để nhận diện
   */
  @Column({ name: 'name', length: 255 })
  name: string;

  /**
   * Mô tả chi tiết về chức năng và mục đích của tool
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  /**
   * ID của nhân viên tạo tool
   */
  @Column({ name: 'created_by', type: 'integer' })
  createdBy: number;

  /**
   * ID của nhân viên cập nhật tool gần nhất
   */
  @Column({ name: 'updated_by', type: 'integer' })
  updatedBy: number;

  /**
   * Thời điểm tạo tool, tính bằng millisecond
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật cuối, tính bằng millisecond
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;

  /**
   * Trạng thái của tool: DRAFT, APPROVED, DEPRECATED
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: ToolStatusEnum,
    default: ToolStatusEnum.DRAFT
  })
  status: ToolStatusEnum;

  /**
   * Loại quyền truy cập: PUBLIC, PRIVATE, RESTRICTED
   */
  @Column({
    name: 'access_type',
    type: 'enum',
    enum: AccessTypeEnum,
    default: AccessTypeEnum.PUBLIC
  })
  accessType: AccessTypeEnum;

  /**
   * Trạng thái đang bán: true nếu tool đang được bán, false nếu không
   */
  @Column({
    name: 'is_for_sale',
    type: 'boolean',
    default: false
  })
  isForSale: boolean;

  /**
   * ID của version mặc định của tool
   */
  @Column({ name: 'version_default', type: 'uuid', nullable: true })
  versionDefault: string | null;

  /**
   * Thời điểm xóa mềm (nếu có)
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;

  /**
   * ID của nhân viên xóa tool
   */
  @Column({ name: 'deleted_by', type: 'int', nullable: true })
  deletedBy: number | null;
}
