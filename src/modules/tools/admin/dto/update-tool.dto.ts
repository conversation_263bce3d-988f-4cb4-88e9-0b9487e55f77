import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString, MaxLength } from 'class-validator';
import { ToolStatusEnum } from '@/modules/tools/constants';
import { AccessTypeEnum } from '@/modules/tools/constants';

/**
 * DTO cho việc cập nhật thông tin tool
 */
export class UpdateToolDto {
  @ApiProperty({
    description: 'Tên hiển thị của tool',
    example: 'Công cụ tìm kiếm nâng cao',
    required: false,
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  name?: string;

  @ApiProperty({
    description: 'Mô tả về tool',
    example: 'Công cụ giúp tìm kiếm thông tin từ nhiều nguồn với các bộ lọc nâng cao',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Trạng thái của tool',
    enum: ToolStatusEnum,
    required: false,
  })
  @IsEnum(ToolStatusEnum)
  @IsOptional()
  status?: ToolStatusEnum;

  @ApiProperty({
    description: 'Loại quyền truy cập',
    enum: AccessTypeEnum,
    required: false,
  })
  @IsEnum(AccessTypeEnum)
  @IsOptional()
  accessType?: AccessTypeEnum;

  @ApiProperty({
    description: 'ID của nhóm tool',
    example: 1,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  groupId?: number;

  @ApiProperty({
    description: 'ID của phiên bản mặc định',
    example: '550e8400-e29b-41d4-a716-446655440000',
    required: false,
  })
  @IsString()
  @IsOptional()
  versionDefault?: string;
}
