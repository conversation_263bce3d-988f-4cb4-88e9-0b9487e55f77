import { ToolStatusEnum } from '@/modules/tools/constants';
import { EmployeeInfoRepository } from '@/modules/tools/repositories/employee-info.repository';
import { AppException } from '@common/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { AdminToolVersion } from '../../entities';
import { TOOLS_ERROR_CODES } from '../../exceptions';
import {
  AdminToolRepository,
  AdminToolVersionRepository,
  UserToolRepository,
} from '../../repositories';
import {
  CreateToolVersionDto,
  EmployeeInfoDto,
  UpdateToolVersionDto,
  VersionDto,
} from '../dto';

@Injectable()
export class AdminToolVersionService {
  private readonly logger = new Logger(AdminToolVersionService.name);

  constructor(
    private readonly adminToolRepository: AdminToolRepository,
    private readonly adminToolVersionRepository: AdminToolVersionRepository,
    private readonly employeeInfoRepository: EmployeeInfoRepository,
    private readonly userToolRepository: UserToolRepository,
  ) { }

  /**
   * Tạo phiên bản mới cho tool
   * @param toolId ID của tool
   * @param employeeId ID của nhân viên tạo phiên bản
   * @param createDto Dữ liệu tạo phiên bản
   * @returns ID của phiên bản đã tạo
   */
  @Transactional()
  async createVersion(
    toolId: string,
    employeeId: number,
    createDto: CreateToolVersionDto,
  ): Promise<string> {
    try {
      // Kiểm tra tool có tồn tại không
      const tool = await this.adminToolRepository.findToolById(toolId);
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra nếu tool đã bị xóa (DEPRECATED), không cho phép tạo phiên bản mới
      if (tool.status === ToolStatusEnum.DEPRECATED) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_AVAILABLE,
          'Tool này đã bị xóa và không còn khả dụng.'
        );
      }

      // Kiểm tra tính hợp lệ của toolName
      const toolNameValidation =
        await this.adminToolRepository.validateToolName(
          createDto.toolName,
          toolId,
        );
      if (!toolNameValidation.valid) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NAME_INVALID,
          toolNameValidation.reason,
        );
      }

      // Tạo phiên bản mới
      const newVersion = new AdminToolVersion();
      newVersion.toolId = toolId;
      newVersion.toolName = createDto.toolName;
      newVersion.toolDescription = createDto.toolDescription || null;
      newVersion.parameters = createDto.parameters;
      newVersion.changeDescription = createDto.changeDescription || null;
      newVersion.status = createDto.status || ToolStatusEnum.DRAFT;
      newVersion.versionName = createDto.versionName; // Sử dụng versionName từ DTO hoặc giá trị mặc định
      newVersion.isDefault = createDto.isDefault || false; // Set isDefault từ DTO
      newVersion.createdBy = employeeId;
      newVersion.updatesBy = employeeId;

      // Lưu phiên bản
      const savedVersion =
        await this.adminToolVersionRepository.save(newVersion);

      // Nếu version này được đánh dấu là default, cập nhật logic default
      if (createDto.isDefault) {
        await this.setVersionAsDefault(toolId, savedVersion.id);
      }

      // Cập nhật trạng thái isUpdate cho tất cả user tools đã clone từ tool này
      try {
        await this.userToolRepository.updateIsUpdateStatus(toolId, true);
        this.logger.log(`Đánh dấu user tools có bản cập nhật mới từ admin tool ${toolId}`);
      } catch (updateError) {
        // Ghi log lỗi nhưng không ảnh hưởng đến việc tạo phiên bản
        this.logger.error(`Lỗi khi cập nhật trạng thái cho user tools: ${updateError.message}`, updateError.stack);
      }

      return savedVersion.id;
    } catch (error) {
      this.logger.error(
        `Failed to create version: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_VERSION_CREATION_FAILED,
        error.message,
      );
    }
  }

  /**
   * Lấy thông tin phiên bản
   * @param versionId ID của phiên bản
   * @returns Thông tin phiên bản
   */
  async getVersionById(versionId: string): Promise<VersionDto> {
    try {
      // Lấy thông tin phiên bản
      const version =
        await this.adminToolVersionRepository.findVersionById(versionId);
      if (!version) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      // Lấy thông tin người tạo
      const createdBy = await this.employeeInfoRepository.getEmployeeInfo(
        version.createdBy,
      );

      // Chuyển đổi sang DTO
      return this.mapVersionToDto(version, createdBy);
    } catch (error) {
      this.logger.error(
        `Failed to get version by ID: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND,
        error.message,
      );
    }
  }

  /**
   * Cập nhật thông tin phiên bản
   * @param versionId ID của phiên bản cần cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @param updateDto Dữ liệu cập nhật
   * @returns ID của phiên bản đã cập nhật
   */
  @Transactional()
  async updateVersion(
    versionId: string,
    employeeId: number,
    updateDto: UpdateToolVersionDto,
  ): Promise<string> {
    try {
      // Lấy thông tin phiên bản
      const version =
        await this.adminToolVersionRepository.findVersionById(versionId);
      if (!version) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      // Kiểm tra tool có tồn tại không
      const tool = await this.adminToolRepository.findToolById(version.toolId);
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra nếu tool đã bị xóa (DEPRECATED), không cho phép cập nhật phiên bản
      if (tool.status === ToolStatusEnum.DEPRECATED) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_AVAILABLE,
          'Tool này đã bị xóa và không còn khả dụng.'
        );
      }

      // Kiểm tra tính hợp lệ của toolName (nếu có cập nhật)
      if (
        updateDto.toolName &&
        updateDto.toolName !== version.toolName
      ) {
        const functionNameValidation =
          await this.adminToolRepository.validateToolName(
            updateDto.toolName,
            version.toolId,
          );
        if (!functionNameValidation.valid) {
          throw new AppException(
            TOOLS_ERROR_CODES.TOOL_NAME_INVALID,
            functionNameValidation.reason,
          );
        }
      }

      // Cập nhật thông tin phiên bản
      if (updateDto.toolName) version.toolName = updateDto.toolName;
      if (updateDto.toolDescription !== undefined)
        version.toolDescription = updateDto.toolDescription;
      if (updateDto.parameters) version.parameters = updateDto.parameters;
      if (updateDto.changeDescription !== undefined)
        version.changeDescription = updateDto.changeDescription;
      if (updateDto.status) version.status = updateDto.status;

      version.updatesBy = employeeId;
      version.updatedAt = Date.now();

      // Lưu phiên bản
      await this.adminToolVersionRepository.save(version);

      // Nếu có cập nhật isDefault, xử lý logic default
      if (updateDto.isDefault !== undefined) {
        if (updateDto.isDefault) {
          // Đặt version này làm default
          await this.setVersionAsDefault(version.toolId, version.id);
        } else {
          // Nếu version hiện tại đang là default và được set thành false
          // Cần chọn version khác làm default
          const currentDefault = await this.adminToolVersionRepository.findDefaultVersion(version.toolId);
          if (currentDefault && currentDefault.id === version.id) {
            // Tìm version mới nhất khác để làm default
            const newDefaultVersionId = await this.adminToolVersionRepository.selectNewDefaultVersion(
              version.toolId,
              version.id
            );
            if (newDefaultVersionId) {
              await this.adminToolRepository.updateDefaultVersion(version.toolId, newDefaultVersionId);
            }
          }
        }
      }

      // Cập nhật trạng thái isUpdate cho tất cả user tools đã clone từ tool này
      try {
        await this.userToolRepository.updateIsUpdateStatus(version.toolId, true);
        this.logger.log(`Đánh dấu user tools có bản cập nhật mới từ admin tool ${version.toolId}`);
      } catch (updateError) {
        // Ghi log lỗi nhưng không ảnh hưởng đến việc cập nhật phiên bản
        this.logger.error(`Lỗi khi cập nhật trạng thái cho user tools: ${updateError.message}`, updateError.stack);
      }

      return version.id;
    } catch (error) {
      this.logger.error(
        `Failed to update version: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_VERSION_UPDATE_FAILED,
        error.message,
      );
    }
  }

  /**
   * Đặt phiên bản làm mặc định
   * @param functionId ID của tool
   * @param versionId ID của phiên bản
   * @param employeeId ID của nhân viên
   * @returns true nếu thành công
   */
  @Transactional()
  async setDefaultVersion(
    toolId: string,
    versionId: string,
    employeeId: number,
  ): Promise<boolean> {
    try {
      // Kiểm tra tool có tồn tại không
      const tool = await this.adminToolRepository.findToolById(toolId);
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra nếu tool đã bị xóa (DEPRECATED), không cho phép đặt phiên bản mặc định
      if (tool.status === ToolStatusEnum.DEPRECATED) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_AVAILABLE,
          'Tool này đã bị xóa và không còn khả dụng.'
        );
      }

      // Kiểm tra phiên bản có tồn tại không và thuộc về tool không
      const version =
        await this.adminToolVersionRepository.findVersionById(versionId);
      if (!version || version.toolId !== toolId) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      // Kiểm tra trạng thái của phiên bản
      if (version.status === ToolStatusEnum.DEPRECATED) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_VERSION_INVALID,
          'Không thể đặt phiên bản đã bị xóa làm mặc định',
        );
      }

      // Đặt version này làm default và clear default của các version khác
      await this.adminToolVersionRepository.updateDefaultVersionStatus(toolId, versionId);

      // Cập nhật thông tin tool
      tool.updatedBy = employeeId;
      tool.updatedAt = Date.now();
      tool.versionDefault = versionId;

      // Lưu tool
      await this.adminToolRepository.save(tool);

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to set default version: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED,
        error.message,
      );
    }
  }

  /**
   * Gỡ phiên bản (xóa mềm)
   * @param versionId ID của phiên bản cần gỡ
   * @param employeeId ID của nhân viên
   * @returns true nếu thành công
   */
  @Transactional()
  async removeVersion(versionId: string, employeeId: number): Promise<boolean> {
    try {
      // Lấy thông tin phiên bản
      const version =
        await this.adminToolVersionRepository.findVersionById(versionId);
      if (!version) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      // Kiểm tra tool có tồn tại không
      const tool = await this.adminToolRepository.findToolById(
        version.toolId,
      );
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra nếu tool đã bị xóa (DEPRECATED), không cho phép xóa phiên bản
      if (tool.status === ToolStatusEnum.DEPRECATED) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_AVAILABLE,
          'Tool này đã bị xóa và không còn khả dụng.'
        );
      }

      // Kiểm tra xem version này có phải là default không
      const isDefaultVersion = version.isDefault;

      // Cập nhật trạng thái phiên bản thành DEPRECATED
      version.status = ToolStatusEnum.DEPRECATED;
      version.isDefault = false; // Không còn là default nữa
      version.updatesBy = employeeId;
      version.updatedAt = Date.now();

      // Lưu phiên bản
      await this.adminToolVersionRepository.save(version);

      // Nếu version bị xóa là default, cần chọn version khác làm default
      if (isDefaultVersion) {
        const newDefaultVersionId = await this.adminToolVersionRepository.selectNewDefaultVersion(
          version.toolId,
          version.id
        );

        if (newDefaultVersionId) {
          await this.adminToolRepository.updateDefaultVersion(version.toolId, newDefaultVersionId);
          this.logger.log(`Selected new default version ${newDefaultVersionId} for tool ${version.toolId}`);
        } else {
          // Không còn version nào khả dụng, set versionDefault = null
          await this.adminToolRepository.updateDefaultVersion(version.toolId, null);
          this.logger.log(`No available versions left for tool ${version.toolId}, set versionDefault to null`);
        }
      }

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to remove version: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_VERSION_DELETE_FAILED,
        error.message,
      );
    }
  }

  /**
   * Đặt version làm mặc định (helper method)
   * @param toolId ID của tool
   * @param versionId ID của version
   * @returns Promise<void>
   */
  private async setVersionAsDefault(toolId: string, versionId: string): Promise<void> {
    try {
      // Cập nhật trạng thái isDefault cho tất cả versions của tool
      await this.adminToolVersionRepository.updateDefaultVersionStatus(toolId, versionId);

      // Cập nhật versionDefault cho tool
      await this.adminToolRepository.updateDefaultVersion(toolId, versionId);

      this.logger.log(`Set version ${versionId} as default for tool ${toolId}`);
    } catch (error) {
      this.logger.error(`Error setting version as default: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Chuyển đổi phiên bản sang DTO
   * @param version Phiên bản
   * @param createdBy Thông tin người tạo
   * @returns DTO của phiên bản
   */
  private mapVersionToDto(
    version: AdminToolVersion,
    createdBy: EmployeeInfoDto,
  ): VersionDto {
    const versionDto = new VersionDto();
    versionDto.id = version.id;
    versionDto.versionName = version.versionName;
    versionDto.toolName = version.toolName;
    versionDto.toolDescription = version.toolDescription;
    versionDto.parameters = version.parameters;
    versionDto.changeDescription = version.changeDescription;
    versionDto.status = version.status;
    versionDto.createdAt = version.createdAt;
    versionDto.createdBy = createdBy;
    return versionDto;
  }
}
