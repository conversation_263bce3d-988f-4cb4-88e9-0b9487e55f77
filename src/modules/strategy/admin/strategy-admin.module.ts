import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CdnService } from '@shared/services/cdn.service';
import { RankStrategy, StrategyAgent, StrategyAgentVersion, StrategyContentStep, ToolsStrategy, UserStrategyAgent, UserStrategyVersionContent } from '../entities';
import {
  RankStrategyRepository,
  StrategyAgentRepository,
  StrategyAgentVersionRepository,
  StrategyContentStepRepository,
  ToolsStrategyRepository,
  UserStrategyAgentRepository,
  UserStrategyVersionContentRepository
} from '../repositories';
import { RankStrategyAdminController, StrategyAgentAdminController, StrategyAgentVersionAdminController } from './controllers';
import { RankStrategyAdminService, StrategyAgentAdminService, StrategyAgentVersionAdminService } from './services';

@Module({
  imports: [TypeOrmModule.forFeature([
    StrategyAgent,
    StrategyAgentVersion,
    UserStrategyAgent,
    StrategyContentStep,
    UserStrategyVersionContent,
    RankStrategy,
    ToolsStrategy
  ])],
  controllers: [
    StrategyAgentAdminController,
    StrategyAgentVersionAdminController,
    RankStrategyAdminController
  ],
  providers: [
    StrategyAgentAdminService,
    StrategyAgentVersionAdminService,
    RankStrategyAdminService,
    StrategyAgentRepository,
    StrategyAgentVersionRepository,
    UserStrategyAgentRepository,
    StrategyContentStepRepository,
    UserStrategyVersionContentRepository,
    RankStrategyRepository,
    ToolsStrategyRepository,
    CdnService
  ],
  exports: [StrategyAgentAdminService, StrategyAgentVersionAdminService, RankStrategyAdminService]
})
export class StrategyAdminModule { }
