import { ApiProperty } from '@nestjs/swagger';
import { Blog } from '@modules/blog/entities';
import { AuthorTypeEnum, BlogStatusEnum } from '../enums';

export class BlogSchema {
  @ApiProperty({
    description: 'ID của bài viết',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Tiêu đề bài viết',
    example: 'Hướng dẫn sử dụng NestJS',
    nullable: true,
  })
  title: string;

  @ApiProperty({
    description: 'Đường link của file content trên CDN',
    example: 'https://cdn.example.com/blogs/content-123.html',
    nullable: true,
  })
  content: string;

  @ApiProperty({
    description: 'Số point để mua bài viết',
    example: 100,
    nullable: true,
  })
  point: number;

  @ApiProperty({
    description: 'Số lượt xem',
    example: 1000,
    nullable: true,
  })
  viewCount: number;

  @ApiProperty({
    description: 'Ảnh tiêu đề',
    example: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
    nullable: true,
  })
  thumbnailUrl: string;

  @ApiProperty({
    description: 'Nhãn',
    example: ['nestjs', 'typescript', 'backend'],
    nullable: true,
  })
  tags: string[];

  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  updatedAt: number;

  @ApiProperty({
    description: 'ID của tác giả nếu đây là bài viết của người dùng',
    example: 1,
    nullable: true,
  })
  userId: number;

  @ApiProperty({
    description: 'Mã của nhân viên nếu đây là bài viết của hệ thống',
    example: 1,
    nullable: true,
  })
  employeeId: number;

  @ApiProperty({
    description: 'Nhân viên kiểm duyệt bài viết nếu đây là bài viết của người dùng',
    example: 1,
    nullable: true,
  })
  employeeModerator: number;

  @ApiProperty({
    description: 'Loại của tác giả bài viết',
    example: AuthorTypeEnum.USER,
    enum: AuthorTypeEnum,
    nullable: true,
  })
  authorType: AuthorTypeEnum;

  @ApiProperty({
    description: 'Trạng thái của bài viết',
    example: BlogStatusEnum.APPROVED,
    enum: BlogStatusEnum,
  })
  status: BlogStatusEnum;

  @ApiProperty({
    description: 'Trạng thái hiển thị của bài viết',
    example: true,
  })
  enable: boolean;

  @ApiProperty({
    description: 'Số lượt like',
    example: 500,
    nullable: true,
  })
  like: number;

  constructor(partial: Partial<Blog>) {
    Object.assign(this, partial);
  }
}

export class BlogListResponseSchema {
  @ApiProperty({
    description: 'Danh sách bài viết',
    type: [BlogSchema],
  })
  items: BlogSchema[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    type: 'object',
    properties: {
      totalItems: {
        type: 'number',
        example: 100,
        description: 'Tổng số bài viết',
      },
      itemCount: {
        type: 'number',
        example: 10,
        description: 'Số bài viết trên trang hiện tại',
      },
      itemsPerPage: {
        type: 'number',
        example: 10,
        description: 'Số bài viết trên mỗi trang',
      },
      totalPages: {
        type: 'number',
        example: 10,
        description: 'Tổng số trang',
      },
      currentPage: {
        type: 'number',
        example: 1,
        description: 'Trang hiện tại',
      },
    },
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
