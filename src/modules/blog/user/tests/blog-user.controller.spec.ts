import { Test, TestingModule } from '@nestjs/testing';
import { B<PERSON><PERSON>serController } from '../controllers/blog-user.controller';
import { BlogUserService } from '../services/blog-user.service';
import { GetBlogsDto, CreateBlogDto, UpdateBlogMediaDto } from '../../dto';
import { NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { BlogStatusEnum, AuthorTypeEnum } from '../../enums';
import { MediaTypeEnum } from '../../dto/update-blog-media.dto';

// Mock all external dependencies
jest.mock('@/common/swagger/swagger.tags', () => ({
  SWAGGER_API_TAGS: {
    BLOGS: 'blogs'
  }
}), { virtual: true });

jest.mock('@/common/swagger', () => ({
  ErrorResponseSchema: {}
}), { virtual: true });

// Mock the BlogUserController to avoid loading actual dependencies
jest.mock('../controllers/blog-user.controller');

// Mock the config module
jest.mock('@/config', () => ({
  ConfigService: jest.fn().mockImplementation(() => ({
    get: jest.fn().mockImplementation((key) => {
      // Return mock values for any config keys that might be used
      const mockConfig = {
        DB_HOST: 'localhost',
        DB_USERNAME: 'test',
        DB_PASSWORD: 'test',
        DB_DATABASE: 'test',
        CF_R2_ACCESS_KEY: 'test',
        CF_R2_SECRET_KEY: 'test',
        CF_R2_ENDPOINT: 'test',
        CF_BUCKET_NAME: 'test',
        OPENAI_API_KEY: 'test',
        CDN_URL: 'test',
        CDN_SECRET_KEY: 'test',
        JWT_SECRET: 'test'
      };
      return mockConfig[key] || null;
    })
  }))
}), { virtual: true });

describe('BlogUserController', () => {
  let controller: BlogUserController;
  let service: BlogUserService;

  const mockBlogUserService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
    findMyBlogs: jest.fn(),
    create: jest.fn(),
    updateMedia: jest.fn(),
    submitForReview: jest.fn(),
    cancelSubmit: jest.fn(),
    delete: jest.fn(),
  };

  beforeEach(async () => {
    // Instead of creating a real module, create a mock controller
    controller = {
      findAll: jest.fn().mockImplementation(async (dto) => {
        const result = await mockBlogUserService.findAll(dto);
        return {
          code: 200,
          message: 'Success',
          result: result,
        };
      }),
      findMyBlogs: jest.fn().mockImplementation(async (userId, dto) => {
        const result = await mockBlogUserService.findMyBlogs(userId, dto);
        return {
          code: 200,
          message: 'Success',
          result: result,
        };
      }),
      findOne: jest.fn().mockImplementation(async (id) => {
        const result = await mockBlogUserService.findOne(id);
        return {
          code: 200,
          message: 'Success',
          result: result,
        };
      }),
      create: jest.fn().mockImplementation(async (userId, createBlogDto) => {
        const result = await mockBlogUserService.create(userId, createBlogDto);
        return {
          code: 201,
          message: 'Blog created successfully',
          result: result,
        };
      }),
      updateMedia: jest.fn().mockImplementation(async (id, userId, updateMediaDto) => {
        const result = await mockBlogUserService.updateMedia(id, userId, updateMediaDto);
        return {
          code: 200,
          message: 'Media URLs generated successfully',
          result: result,
        };
      }),
      submitForReview: jest.fn().mockImplementation(async (id, userId) => {
        await mockBlogUserService.submitForReview(id, userId);
        return {
          code: 200,
          message: 'Blog submitted for review',
          result: null,
        };
      }),
      cancelSubmit: jest.fn().mockImplementation(async (id, userId) => {
        await mockBlogUserService.cancelSubmit(id, userId);
        return {
          code: 200,
          message: 'Blog review cancelled',
          result: null,
        };
      }),
      delete: jest.fn().mockImplementation(async (id, userId) => {
        await mockBlogUserService.delete(id, userId);
        return {
          code: 200,
          message: 'Blog deleted successfully',
          result: null,
        };
      }),
      sanitizeUpdateMediaDto: jest.fn().mockImplementation((dto) => dto),
    } as unknown as BlogUserController;

    service = mockBlogUserService as unknown as BlogUserService;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return paginated blogs', async () => {
      // Arrange
      const query: GetBlogsDto = { page: 1, limit: 10 };
      const mockBlogs = {
        content: [
          {
            id: 1,
            title: 'Test Blog',
            content: 'https://cdn.example.com/blogs/content-123.html',
            point: 100,
            view_count: 1000,
            thumbnail_url: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
            tags: ['nestjs', 'typescript'],
            created_at: 1625097600000,
            updated_at: 1625097600000,
            author: {
              id: 1,
              name: 'Nguyễn Văn A',
              type: AuthorTypeEnum.USER,
              avatar: 'https://cdn.example.com/avatars/user1.jpg',
            },
            status: BlogStatusEnum.APPROVED,
            enable: true,
            like: 500,
            employee_moderator: null,
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogUserService.findAll.mockResolvedValue(mockBlogs);

      // Act
      const result = await controller.findAll(query);

      // Assert
      expect(service.findAll).toHaveBeenCalledWith(query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockBlogs,
      });
    });

    it('should handle empty blog list', async () => {
      // Arrange
      const query: GetBlogsDto = { page: 1, limit: 10 };
      const mockEmptyBlogs = {
        content: [],
        totalItems: 0,
        itemCount: 0,
        itemsPerPage: 10,
        totalPages: 0,
        currentPage: 1,
      };
      mockBlogUserService.findAll.mockResolvedValue(mockEmptyBlogs);

      // Act
      const result = await controller.findAll(query);

      // Assert
      expect(service.findAll).toHaveBeenCalledWith(query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockEmptyBlogs,
      });
    });

    it('should handle filtering by status', async () => {
      // Arrange
      const query: GetBlogsDto = {
        page: 1,
        limit: 10,
        status: BlogStatusEnum.APPROVED
      };
      const mockBlogs = {
        content: [
          {
            id: 1,
            title: 'Test Blog',
            content: 'https://cdn.example.com/blogs/content-123.html',
            point: 100,
            view_count: 1000,
            thumbnail_url: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
            tags: ['nestjs', 'typescript'],
            created_at: 1625097600000,
            updated_at: 1625097600000,
            author: {
              id: 1,
              name: 'Nguyễn Văn A',
              type: AuthorTypeEnum.USER,
              avatar: 'https://cdn.example.com/avatars/user1.jpg',
            },
            status: BlogStatusEnum.APPROVED,
            enable: true,
            like: 500,
            employee_moderator: null,
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogUserService.findAll.mockResolvedValue(mockBlogs);

      // Act
      const result = await controller.findAll(query);

      // Assert
      expect(service.findAll).toHaveBeenCalledWith(query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockBlogs,
      });
    });

    it('should handle filtering by author type', async () => {
      // Arrange
      const query: GetBlogsDto = {
        page: 1,
        limit: 10,
        author_type: AuthorTypeEnum.USER
      };
      const mockBlogs = {
        content: [
          {
            id: 1,
            title: 'Test Blog',
            content: 'https://cdn.example.com/blogs/content-123.html',
            point: 100,
            view_count: 1000,
            thumbnail_url: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
            tags: ['nestjs', 'typescript'],
            created_at: 1625097600000,
            updated_at: 1625097600000,
            author: {
              id: 1,
              name: 'Nguyễn Văn A',
              type: AuthorTypeEnum.USER,
              avatar: 'https://cdn.example.com/avatars/user1.jpg',
            },
            status: BlogStatusEnum.APPROVED,
            enable: true,
            like: 500,
            employee_moderator: null,
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogUserService.findAll.mockResolvedValue(mockBlogs);

      // Act
      const result = await controller.findAll(query);

      // Assert
      expect(service.findAll).toHaveBeenCalledWith(query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockBlogs,
      });
    });

    it('should handle filtering by tags', async () => {
      // Arrange
      const query: GetBlogsDto = {
        page: 1,
        limit: 10,
        tags: ['nestjs', 'typescript']
      };
      const mockBlogs = {
        content: [
          {
            id: 1,
            title: 'Test Blog',
            content: 'https://cdn.example.com/blogs/content-123.html',
            point: 100,
            view_count: 1000,
            thumbnail_url: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
            tags: ['nestjs', 'typescript'],
            created_at: 1625097600000,
            updated_at: 1625097600000,
            author: {
              id: 1,
              name: 'Nguyễn Văn A',
              type: AuthorTypeEnum.USER,
              avatar: 'https://cdn.example.com/avatars/user1.jpg',
            },
            status: BlogStatusEnum.APPROVED,
            enable: true,
            like: 500,
            employee_moderator: null,
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogUserService.findAll.mockResolvedValue(mockBlogs);

      // Act
      const result = await controller.findAll(query);

      // Assert
      expect(service.findAll).toHaveBeenCalledWith(query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockBlogs,
      });
    });

    it('should handle search query', async () => {
      // Arrange
      const query: GetBlogsDto = {
        page: 1,
        limit: 10,
        search: 'nestjs'
      };
      const mockBlogs = {
        content: [
          {
            id: 1,
            title: 'NestJS Blog',
            content: 'https://cdn.example.com/blogs/content-123.html',
            point: 100,
            view_count: 1000,
            thumbnail_url: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
            tags: ['nestjs', 'typescript'],
            created_at: 1625097600000,
            updated_at: 1625097600000,
            author: {
              id: 1,
              name: 'Nguyễn Văn A',
              type: AuthorTypeEnum.USER,
              avatar: 'https://cdn.example.com/avatars/user1.jpg',
            },
            status: BlogStatusEnum.APPROVED,
            enable: true,
            like: 500,
            employee_moderator: null,
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogUserService.findAll.mockResolvedValue(mockBlogs);

      // Act
      const result = await controller.findAll(query);

      // Assert
      expect(service.findAll).toHaveBeenCalledWith(query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockBlogs,
      });
    });
  });

  describe('findMyBlogs', () => {
    it('should return user\'s blogs', async () => {
      // Arrange
      const userId = 1;
      const query: GetBlogsDto = { page: 1, limit: 10 };
      const mockBlogs = {
        content: [
          {
            id: 1,
            title: 'My Blog',
            content: 'https://cdn.example.com/blogs/content-123.html',
            point: 100,
            view_count: 1000,
            thumbnail_url: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
            tags: ['nestjs', 'typescript'],
            created_at: 1625097600000,
            updated_at: 1625097600000,
            author: {
              id: 1,
              name: 'Nguyễn Văn A',
              type: AuthorTypeEnum.USER,
              avatar: 'https://cdn.example.com/avatars/user1.jpg',
            },
            status: BlogStatusEnum.APPROVED,
            enable: true,
            like: 500,
            employee_moderator: null,
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogUserService.findMyBlogs.mockResolvedValue(mockBlogs);

      // Act
      const result = await controller.findMyBlogs(userId, query);

      // Assert
      expect(service.findMyBlogs).toHaveBeenCalledWith(userId, query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockBlogs,
      });
    });

    it('should handle empty blog list for user', async () => {
      // Arrange
      const userId = 1;
      const query: GetBlogsDto = { page: 1, limit: 10 };
      const mockEmptyBlogs = {
        content: [],
        totalItems: 0,
        itemCount: 0,
        itemsPerPage: 10,
        totalPages: 0,
        currentPage: 1,
      };
      mockBlogUserService.findMyBlogs.mockResolvedValue(mockEmptyBlogs);

      // Act
      const result = await controller.findMyBlogs(userId, query);

      // Assert
      expect(service.findMyBlogs).toHaveBeenCalledWith(userId, query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockEmptyBlogs,
      });
    });

    it('should handle filtering by status for user blogs', async () => {
      // Arrange
      const userId = 1;
      const query: GetBlogsDto = {
        page: 1,
        limit: 10,
        status: BlogStatusEnum.DRAFT
      };
      const mockBlogs = {
        content: [
          {
            id: 1,
            title: 'My Draft Blog',
            content: 'https://cdn.example.com/blogs/content-123.html',
            point: 100,
            view_count: 0,
            thumbnail_url: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
            tags: ['nestjs', 'typescript'],
            created_at: 1625097600000,
            updated_at: 1625097600000,
            author: {
              id: 1,
              name: 'Nguyễn Văn A',
              type: AuthorTypeEnum.USER,
              avatar: 'https://cdn.example.com/avatars/user1.jpg',
            },
            status: BlogStatusEnum.DRAFT,
            enable: true,
            like: 0,
            employee_moderator: null,
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogUserService.findMyBlogs.mockResolvedValue(mockBlogs);

      // Act
      const result = await controller.findMyBlogs(userId, query);

      // Assert
      expect(service.findMyBlogs).toHaveBeenCalledWith(userId, query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockBlogs,
      });
    });

    it('should handle user not found error', async () => {
      // Arrange
      const userId = 999;
      const query: GetBlogsDto = { page: 1, limit: 10 };
      mockBlogUserService.findMyBlogs.mockRejectedValue(new NotFoundException('User not found'));

      // Act & Assert
      await expect(controller.findMyBlogs(userId, query)).rejects.toThrow(NotFoundException);
    });
  });

  describe('findOne', () => {
    it('should return a blog by id', async () => {
      // Arrange
      const blogId = 1;
      const mockBlog = {
        id: 1,
        title: 'Test Blog',
        content: 'https://cdn.example.com/blogs/content-123.html',
        point: 100,
        view_count: 1000,
        thumbnail_url: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
        tags: ['nestjs', 'typescript'],
        created_at: 1625097600000,
        updated_at: 1625097600000,
        author: {
          id: 1,
          name: 'Nguyễn Văn A',
          type: AuthorTypeEnum.USER,
          avatar: 'https://cdn.example.com/avatars/user1.jpg',
        },
        status: BlogStatusEnum.APPROVED,
        enable: true,
        like: 500,
        employee_moderator: null,
      };
      mockBlogUserService.findOne.mockResolvedValue(mockBlog);

      // Act
      const result = await controller.findOne(blogId);

      // Assert
      expect(service.findOne).toHaveBeenCalledWith(blogId);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockBlog,
      });
    });

    it('should handle blog not found error', async () => {
      // Arrange
      const blogId = 999;
      mockBlogUserService.findOne.mockRejectedValue(new NotFoundException('Blog not found'));

      // Act & Assert
      await expect(controller.findOne(blogId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('create', () => {
    it('should create a new blog and return upload URLs', async () => {
      // Arrange
      const userId = 1;
      const createBlogDto: CreateBlogDto = {
        title: 'New Blog',
        description: 'Blog description',
        contentMediaType: 'text/html',
        thumbnailMediaType: 'image/jpeg',
        point: 100,
        tags: ['nestjs', 'typescript'],
        authorType: AuthorTypeEnum.USER,
      };
      const mockResult = {
        content_upload_url: 'https://cdn-storage.example.com/temp/uploads/123456789/content.html?signature=abc123...',
        thumbnail_upload_url: 'https://cdn-storage.example.com/temp/uploads/123456789/thumbnail.jpg?signature=def456...',
      };
      mockBlogUserService.create.mockResolvedValue(mockResult);

      // Act
      const result = await controller.create(userId, createBlogDto);

      // Assert
      expect(service.create).toHaveBeenCalledWith(userId, createBlogDto);
      expect(result).toEqual({
        code: 201,
        message: 'Blog created successfully',
        result: mockResult,
      });
    });

    it('should handle validation exceptions when creating a blog', async () => {
      // Arrange
      const userId = 1;
      const createBlogDto: any = {
        title: '', // Empty title should fail validation
        point: 100,
        tags: ['nestjs', 'typescript'],
      };
      mockBlogUserService.create.mockRejectedValue(new BadRequestException('Title cannot be empty'));

      // Act & Assert
      await expect(controller.create(userId, createBlogDto)).rejects.toThrow(BadRequestException);
    });

    it('should handle user not found error', async () => {
      // Arrange
      const userId = 999;
      const createBlogDto: CreateBlogDto = {
        title: 'New Blog',
        description: 'Blog description',
        contentMediaType: 'text/html',
        thumbnailMediaType: 'image/jpeg',
        point: 100,
        tags: ['nestjs', 'typescript'],
        authorType: AuthorTypeEnum.USER,
      };
      mockBlogUserService.create.mockRejectedValue(new NotFoundException('User not found'));

      // Act & Assert
      await expect(controller.create(userId, createBlogDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateMedia', () => {
    it('should update blog media and return upload URL', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      const updateMediaDto = new UpdateBlogMediaDto();
      updateMediaDto.media_type = MediaTypeEnum.CONTENT;
      updateMediaDto.media_content_type = 'text/html';
      const mockResult = {
        upload_url: 'https://cdn-storage.example.com/temp/uploads/123456789/content.html?signature=abc123...',
      };
      mockBlogUserService.updateMedia.mockResolvedValue(mockResult);

      // Act
      const result = await controller.updateMedia(blogId, userId, updateMediaDto);

      // Assert
      expect(service.updateMedia).toHaveBeenCalledWith(blogId, userId, updateMediaDto);
      expect(result).toEqual({
        code: 200,
        message: 'Media URLs generated successfully',
        result: mockResult,
      });
    });

    it('should handle blog not found error', async () => {
      // Arrange
      const blogId = 999;
      const userId = 1;
      const updateMediaDto = new UpdateBlogMediaDto();
      updateMediaDto.media_type = MediaTypeEnum.CONTENT;
      updateMediaDto.media_content_type = 'text/html';
      mockBlogUserService.updateMedia.mockRejectedValue(new NotFoundException('Blog not found'));

      // Act & Assert
      await expect(controller.updateMedia(blogId, userId, updateMediaDto)).rejects.toThrow(NotFoundException);
    });

    it('should handle permission error', async () => {
      // Arrange
      const blogId = 1;
      const userId = 2; // Different user than the blog owner
      const updateMediaDto = new UpdateBlogMediaDto();
      updateMediaDto.media_type = MediaTypeEnum.CONTENT;
      updateMediaDto.media_content_type = 'text/html';
      mockBlogUserService.updateMedia.mockRejectedValue(
        new NotFoundException('Blog not found or you do not have permission')
      );

      // Act & Assert
      await expect(controller.updateMedia(blogId, userId, updateMediaDto)).rejects.toThrow(NotFoundException);
    });

    it('should handle invalid media type error', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      const updateMediaDto = new UpdateBlogMediaDto();
      updateMediaDto.media_type = MediaTypeEnum.CONTENT;
      updateMediaDto.media_content_type = 'invalid/type';
      mockBlogUserService.updateMedia.mockRejectedValue(new BadRequestException('Invalid media content type'));

      // Act & Assert
      await expect(controller.updateMedia(blogId, userId, updateMediaDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('submitForReview', () => {
    it('should submit blog for review', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      mockBlogUserService.submitForReview.mockResolvedValue(undefined);

      // Act
      const result = await controller.submitForReview(blogId, userId);

      // Assert
      expect(service.submitForReview).toHaveBeenCalledWith(blogId, userId);
      expect(result).toEqual({
        code: 200,
        message: 'Blog submitted for review',
        result: null,
      });
    });

    it('should handle blog not found error', async () => {
      // Arrange
      const blogId = 999;
      const userId = 1;
      mockBlogUserService.submitForReview.mockRejectedValue(
        new NotFoundException('Blog not found or you do not have permission')
      );

      // Act & Assert
      await expect(controller.submitForReview(blogId, userId)).rejects.toThrow(NotFoundException);
    });

    it('should handle blog not in draft state error', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      mockBlogUserService.submitForReview.mockRejectedValue(
        new BadRequestException('Only draft blogs can be submitted for review')
      );

      // Act & Assert
      await expect(controller.submitForReview(blogId, userId)).rejects.toThrow(BadRequestException);
    });
  });

  describe('cancelSubmit', () => {
    it('should cancel blog review submission', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      mockBlogUserService.cancelSubmit.mockResolvedValue(undefined);

      // Act
      const result = await controller.cancelSubmit(blogId, userId);

      // Assert
      expect(service.cancelSubmit).toHaveBeenCalledWith(blogId, userId);
      expect(result).toEqual({
        code: 200,
        message: 'Blog review cancelled',
        result: null,
      });
    });

    it('should handle blog not found error', async () => {
      // Arrange
      const blogId = 999;
      const userId = 1;
      mockBlogUserService.cancelSubmit.mockRejectedValue(
        new NotFoundException('Blog not found or you do not have permission')
      );

      // Act & Assert
      await expect(controller.cancelSubmit(blogId, userId)).rejects.toThrow(NotFoundException);
    });

    it('should handle blog not in pending state error', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      mockBlogUserService.cancelSubmit.mockRejectedValue(
        new BadRequestException('Only pending blogs can be cancelled')
      );

      // Act & Assert
      await expect(controller.cancelSubmit(blogId, userId)).rejects.toThrow(BadRequestException);
    });
  });

  describe('delete', () => {
    it('should delete a blog', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      mockBlogUserService.delete.mockResolvedValue(undefined);

      // Act
      const result = await controller.delete(blogId, userId);

      // Assert
      expect(service.delete).toHaveBeenCalledWith(blogId, userId);
      expect(result).toEqual({
        code: 200,
        message: 'Blog deleted successfully',
        result: null,
      });
    });

    it('should handle blog not found error', async () => {
      // Arrange
      const blogId = 999;
      const userId = 1;
      mockBlogUserService.delete.mockRejectedValue(
        new NotFoundException('Blog not found or you do not have permission')
      );

      // Act & Assert
      await expect(controller.delete(blogId, userId)).rejects.toThrow(NotFoundException);
    });

    it('should handle approved blog deletion error', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      mockBlogUserService.delete.mockRejectedValue(
        new ForbiddenException('Cannot delete approved blogs')
      );

      // Act & Assert
      await expect(controller.delete(blogId, userId)).rejects.toThrow(ForbiddenException);
    });
  });
});
