import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ServicesModule } from '@shared/services/services.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import {
  UserProduct,
  UserClassification,
  CustomField,
  PhysicalWarehouse,
  UserOrder,
  VirtualWarehouse,
  Warehouse,
  Inventory,
  UserConvert,
  UserConvertCustomer,
  UserAddress,
  ProductAdvancedInfo,

  CustomerFacebook,
  CustomerWeb,
  File,
  Folder
} from '../entities';
import { Product } from '@modules/marketplace/entities';
import { Agent } from '@modules/agent/entities/agent.entity';

import {
  UserProductRepository,
  UserOrderRepository,
  WarehouseRepository,
  PhysicalWarehouseRepository,
  VirtualWarehouseRepository,
  InventoryRepository,
  UserClassificationRepository,
  CustomFieldRepository,
  UserConvertRepository,
  UserConvertCustomerRepository,
  UserAddressRepository,
  ProductAdvancedInfoRepository,

  CustomerFacebookRepository,
  CustomerWebRepository,
  FileRepository,
  FolderRepository,
  BusinessReportRepository
} from '../repositories';
import { ProductRepository } from '@modules/marketplace/repositories';

import {
  UserProductController,
  UserOrderController,
  UserWarehouseController,
  UserInventoryController,
  UserPhysicalWarehouseController,
  CustomFieldController,
  BusinessIntegrationController,
  ClassificationController,
  UserConvertController,
  UserConvertCustomerController,
  UserFileController,
  UserFolderController,
  UserVirtualWarehouseController,
  BusinessReportController,
} from './controllers';

import {
  UserProductService,
  UserOrderService,
  UserWarehouseService,
  UserInventoryService,
  UserPhysicalWarehouseService,
  CustomFieldService,
  BusinessIntegrationService,
  ClassificationService,
  UserConvertService,
  UserConvertCustomerService,
  UserFileService,
  UserFolderService,
  UserVirtualWarehouseService,
  BusinessReportService,
} from './services';

import { ValidationHelper, UserProductHelper, BusinessReportHelper, MetadataHelper, ProductValidationHelper } from './helpers';
import { BusinessIntegrationInterceptor } from './interceptors/business-integration.interceptor';

/**
 * Module quản lý chức năng business cho người dùng
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserProduct,
      UserClassification,
      CustomField,
      PhysicalWarehouse,
      UserOrder,
      VirtualWarehouse,
      Warehouse,
      Inventory,
      UserConvert,
      UserConvertCustomer,
      UserAddress,
      ProductAdvancedInfo,

      CustomerFacebook,
      CustomerWeb,
      Product,
      Agent,
      File,
      Folder
    ]),
    ServicesModule
  ],
  controllers: [
    UserProductController,
    UserOrderController,
    UserWarehouseController,
    UserInventoryController,
    UserPhysicalWarehouseController,
    CustomFieldController,
    BusinessIntegrationController,
    ClassificationController,
    UserConvertController,
    UserConvertCustomerController,
    UserFileController,
    UserFolderController,
    UserVirtualWarehouseController,
    BusinessReportController
  ],
  providers: [
    // Repositories
    UserProductRepository,
    UserOrderRepository,
    WarehouseRepository,
    PhysicalWarehouseRepository,
    VirtualWarehouseRepository,
    InventoryRepository,
    UserClassificationRepository,
    CustomFieldRepository,
    UserConvertRepository,
    UserConvertCustomerRepository,
    UserAddressRepository,
    ProductAdvancedInfoRepository,

    CustomerFacebookRepository,
    CustomerWebRepository,
    ProductRepository,
    FileRepository,
    FolderRepository,
    BusinessReportRepository,

    // Helpers
    ValidationHelper,
    UserProductHelper,
    BusinessReportHelper,
    MetadataHelper,
    ProductValidationHelper,

    // Services
    UserProductService,
    UserOrderService,
    UserWarehouseService,
    UserInventoryService,
    UserPhysicalWarehouseService,
    CustomFieldService,
    ClassificationService,
    BusinessIntegrationService,
    UserConvertService,
    UserConvertCustomerService,
    UserFileService,
    UserFolderService,
    UserVirtualWarehouseService,
    BusinessReportService,

    // Interceptors
    {
      provide: APP_INTERCEPTOR,
      useClass: BusinessIntegrationInterceptor
    }
  ],
  exports: [
    TypeOrmModule,
    UserProductService,
    UserOrderService,
    UserWarehouseService,
    UserInventoryService,
    UserPhysicalWarehouseService,
    CustomFieldService,
    ClassificationService,
    BusinessIntegrationService,
    UserConvertService,
    UserConvertCustomerService,
    UserFileService,
    UserFolderService,
    UserVirtualWarehouseService,
    BusinessReportService,
    UserProductRepository,
  ],
})
export class BusinessUserModule {}
