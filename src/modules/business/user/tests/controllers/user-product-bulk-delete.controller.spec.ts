import { Test, TestingModule } from '@nestjs/testing';
import { UserProductController } from '../../controllers/user-product.controller';
import { UserProductService } from '../../services/user-product.service';
import { BulkDeleteProductDto, BulkDeleteProductResponseDto } from '../../dto';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';

describe('UserProductController - Bulk Delete', () => {
  let controller: UserProductController;
  let service: UserProductService;

  const mockUserProductService = {
    bulkDeleteProducts: jest.fn(),
  };

  const mockUser = {
    id: 1,
    email: '<EMAIL>',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserProductController],
      providers: [
        {
          provide: UserProductService,
          useValue: mockUserProductService,
        },
      ],
    }).compile();

    controller = module.get<UserProductController>(UserProductController);
    service = module.get<UserProductService>(UserProductService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('bulkDeleteProducts', () => {
    it('nên xóa nhiều sản phẩm thành công', async () => {
      // Arrange
      const bulkDeleteDto: BulkDeleteProductDto = {
        productIds: [1, 2, 3],
      };

      const expectedResponse: BulkDeleteProductResponseDto = {
        totalRequested: 3,
        successCount: 3,
        failureCount: 0,
        results: [
          { productId: 1, status: 'success', message: 'Xóa sản phẩm thành công' },
          { productId: 2, status: 'success', message: 'Xóa sản phẩm thành công' },
          { productId: 3, status: 'success', message: 'Xóa sản phẩm thành công' },
        ],
        message: 'Xóa thành công 3/3 sản phẩm',
      };

      jest.spyOn(service, 'bulkDeleteProducts').mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.bulkDeleteProducts(bulkDeleteDto, mockUser.id);

      // Assert
      expect(service.bulkDeleteProducts).toHaveBeenCalledWith(bulkDeleteDto, mockUser.id);
      expect(result.data).toEqual(expectedResponse);
      expect(result.message).toBe(expectedResponse.message);
      expect(result.code).toBe(200);
    });

    it('nên trả về status 207 khi có một số sản phẩm thất bại', async () => {
      // Arrange
      const bulkDeleteDto: BulkDeleteProductDto = {
        productIds: [1, 2, 3],
      };

      const expectedResponse: BulkDeleteProductResponseDto = {
        totalRequested: 3,
        successCount: 2,
        failureCount: 1,
        results: [
          { productId: 1, status: 'success', message: 'Xóa sản phẩm thành công' },
          { productId: 2, status: 'success', message: 'Xóa sản phẩm thành công' },
          { productId: 3, status: 'error', message: 'Không tìm thấy sản phẩm với ID 3' },
        ],
        message: 'Xóa thành công 2/3 sản phẩm',
      };

      jest.spyOn(service, 'bulkDeleteProducts').mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.bulkDeleteProducts(bulkDeleteDto, mockUser.id);

      // Assert
      expect(service.bulkDeleteProducts).toHaveBeenCalledWith(bulkDeleteDto, mockUser.id);
      expect(result.data).toEqual(expectedResponse);
      expect(result.message).toBe(expectedResponse.message);
      expect(result.code).toBe(207); // Multi-Status
    });

    it('nên ném lỗi khi service ném AppException', async () => {
      // Arrange
      const bulkDeleteDto: BulkDeleteProductDto = {
        productIds: [1, 2, 3],
      };

      const error = new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_DELETION_FAILED,
        'Lỗi khi xóa bulk sản phẩm'
      );

      jest.spyOn(service, 'bulkDeleteProducts').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.bulkDeleteProducts(bulkDeleteDto, mockUser.id))
        .rejects.toThrow(AppException);
      expect(service.bulkDeleteProducts).toHaveBeenCalledWith(bulkDeleteDto, mockUser.id);
    });
  });
});
