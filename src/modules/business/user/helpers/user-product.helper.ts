import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  CustomFieldRepository,
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import { UserProduct } from '@modules/business/entities';
import { ProductResponseDto } from '../dto';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils';
import { ProductTypeEnum } from '@modules/business/enums';
import { EventFormatEnum } from '../dto/event-product.dto';

@Injectable()
export class UserProductHelper {
  private readonly logger = new Logger(UserProductHelper.name);

  constructor(
    @InjectRepository(CustomFieldRepository)
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Chuyển đổi từ entity sang DTO response
   * @param product Entity sản phẩm
   * @returns DTO response
   */
  async mapToProductResponseDto(
    product: UserProduct,
  ): Promise<ProductResponseDto> {
    try {
      // Chuyển đổi cơ bản
      const responseDto = plainToInstance(ProductResponseDto, product, {
        excludeExtraneousValues: true,
      });

      // Xử lý URL hình ảnh
      if (product.images && product.images.length > 0) {
        try {
          // Xử lý các trường hợp khác nhau của trường images với Promise.all
          responseDto.images = await Promise.all(
            product.images.map(async (img: any, index: number) => {
              let key = '';
              let position = index;

              // Trường hợp img là string
              if (typeof img === 'string') {
                key = img;
              }
              // Trường hợp img là object với key và position
              else if (img && typeof img === 'object') {
                if ('key' in img) {
                  key = img.key;
                }
                if ('position' in img && typeof img.position === 'number') {
                  position = img.position;
                }
              }

              // Kiểm tra key có hợp lệ không
              if (!key) {
                this.logger.warn(`Invalid image key found at position ${position}`);
                return {
                  key: '',
                  position: position,
                  url: ''
                };
              }

              // Sử dụng CDN signed URL như các module khác (marketplace, data/media)
              const url = this.cdnService.generateUrlView(key, TimeIntervalEnum.ONE_HOUR);
              const timestamp = Date.now();

              return {
                key: key,
                position: position,
                url: url ? `${url}?t=${timestamp}` : ''
              };
            })
          );
        } catch (error) {
          this.logger.error(`Lỗi khi xử lý hình ảnh: ${error.message}`, error.stack);
          responseDto.images = [];
        }
      }

      // Xử lý thông tin nâng cao nếu sản phẩm không phải PHYSICAL hoặc COMBO
      if (product.productType !== ProductTypeEnum.PHYSICAL && product.productType !== ProductTypeEnum.COMBO) {
        try {
          const advancedInfo = await this.productAdvancedInfoRepository.findByProductId(product.id);
          if (advancedInfo) {
            // Chuyển đổi thông tin nâng cao theo loại sản phẩm
            switch (product.productType) {
              case ProductTypeEnum.DIGITAL:
                responseDto.advancedInfo = {
                  purchaseCount: advancedInfo.purchaseCount,
                  digitalFulfillmentFlow: advancedInfo.digitalFulfillmentFlow,
                  digitalOutput: advancedInfo.digitalOutput,
                };
                break;

              case ProductTypeEnum.EVENT:
                responseDto.advancedInfo = {
                  purchaseCount: advancedInfo.purchaseCount,
                  eventFormat: advancedInfo.eventFormat as EventFormatEnum,
                  eventLink: advancedInfo.eventLink,
                  eventLocation: advancedInfo.eventLocation,
                  startDate: advancedInfo.startDate,
                  endDate: advancedInfo.endDate,
                  timezone: advancedInfo.timezone,
                  ticketTypes: await this.processTicketTypesForResponse(advancedInfo.ticketTypes, advancedInfo.images),
                };
                break;

              case ProductTypeEnum.SERVICE:
                responseDto.advancedInfo = {
                  purchaseCount: advancedInfo.purchaseCount,
                  servicePackages: await this.processServicePackagesForResponse(advancedInfo.servicePackages, advancedInfo.images),
                };
                break;
            }
          }
        } catch (error) {
          this.logger.warn(`Không thể lấy thông tin nâng cao cho sản phẩm ${product.id}: ${error.message}`);
        }
      }

      // Đã loại bỏ xử lý các trường tùy chỉnh vì không tồn tại trong database

      return responseDto;
    } catch (error) {
      this.logger.error(`Lỗi khi chuyển đổi sản phẩm sang DTO: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_MAPPING_FAILED,
        `Lỗi khi chuyển đổi sản phẩm sang DTO: ${error.message}`,
      );
    }
  }


}