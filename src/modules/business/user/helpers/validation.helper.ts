import { Injectable } from '@nestjs/common';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { CustomField, UserProduct, Warehouse, Inventory, VirtualWarehouse, File, Folder } from '@modules/business/entities';
import { EntityStatusEnum, PriceTypeEnum, WarehouseTypeEnum } from '@modules/business/enums';
import { CreateCustomFieldDto, UpdateCustomFieldDto, MetadataFieldDto} from '../dto';
import {
  WarehouseRepository,
  CustomFieldRepository,
  InventoryRepository,
  UserProductRepository,
  VirtualWarehouseRepository,
  FileRepository,
  FolderRepository,

} from '@modules/business/repositories';
// import { CustomFieldRepository } from '@modules/business/repositories'; // Duplicate import đã xóa
import {

  CreateWarehouseDto,
  UpdateWarehouseDto,
  CreateVirtualWarehouseDto,
  UpdateVirtualWarehouseDto,
} from '../dto/warehouse';
import { CreateInventoryDto, UpdateInventoryDto } from '../dto/inventory';
import { CreateFileDto, UpdateFileDto } from '../dto/file';
import { CreateFolderDto, UpdateFolderDto } from '../dto/folder';

/**
 * Helper xử lý validation cho business module
 */
@Injectable()
export class ValidationHelper {
  constructor(
    private readonly warehouseRepository: WarehouseRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    // private readonly warehouseCustomFieldRepository: WarehouseCustomFieldRepository, // Đã bị xóa
    private readonly inventoryRepository: InventoryRepository,
    private readonly userProductRepository: UserProductRepository,
    private readonly virtualWarehouseRepository: VirtualWarehouseRepository,
    private readonly fileRepository: FileRepository,
    private readonly folderRepository: FolderRepository,

  ) {}
  /**
   * Kiểm tra xem nhóm trường tùy chỉnh có tồn tại không
   * @param groupForm Nhóm trường tùy chỉnh
   * @param id ID của nhóm trường tùy chỉnh (tùy chọn)
   */
  // validateGroupFormExists(groupForm: CustomGroupForm | null, id?: number): void { // CustomGroupForm đã bị xóa
  validateGroupFormExists(groupForm: any | null, id?: number): void {
    if (!groupForm) {
      throw new AppException(
        BUSINESS_ERROR_CODES.GROUP_FORM_NOT_FOUND,
        id ? `Không tìm thấy nhóm trường tùy chỉnh với ID ${id}` : 'Không tìm thấy nhóm trường tùy chỉnh',
      );
    }
  }

  /**
   * Kiểm tra xem trường tùy chỉnh có tồn tại không
   * @param customField Trường tùy chỉnh
   * @param fieldId ID của trường tùy chỉnh
   */
  validateCustomFieldExists(customField: CustomField | null, fieldId: number): asserts customField is CustomField {
    if (!customField) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
        `Không tìm thấy trường tùy chỉnh với ID ${fieldId}`,
      );
    }
  }

  /**
   * Kiểm tra xem userId có được cung cấp không
   * @param userId ID người dùng
   * @param isUpdate Có phải đang cập nhật không
   */
  validateUserProvided(userId: number | null | undefined, isUpdate: boolean = false): asserts userId is number {
    if (!userId && !isUpdate) {
      throw new AppException(
        BUSINESS_ERROR_CODES.GROUP_FORM_CREATION_FAILED,
        'Phải cung cấp userId',
      );
    }
  }

  /**
   * Kiểm tra sản phẩm có tồn tại và không bị xóa
   * @param product Sản phẩm cần kiểm tra
   * @param productId ID của sản phẩm
   * @param isUpdate Có phải đang cập nhật không
   */
  validateProductExists(product: UserProduct | null, productId: number, isUpdate: boolean = false): void {
    if (!product) {
      throw new AppException(
        isUpdate ? BUSINESS_ERROR_CODES.GROUP_FORM_UPDATE_FAILED : BUSINESS_ERROR_CODES.GROUP_FORM_CREATION_FAILED,
        `Không tìm thấy sản phẩm với ID ${productId}`,
      );
    }

    if (product.status === EntityStatusEnum.DELETED) {
      throw new AppException(
        isUpdate ? BUSINESS_ERROR_CODES.GROUP_FORM_UPDATE_FAILED : BUSINESS_ERROR_CODES.GROUP_FORM_CREATION_FAILED,
        `Sản phẩm với ID ${productId} đã bị xóa`,
      );
    }
  }

  /**
   * Kiểm tra tính hợp lệ của dữ liệu tạo trường tùy chỉnh
   * @param createDto DTO chứa thông tin tạo trường tùy chỉnh
   */
  validateCreateCustomFieldData(createDto: CreateCustomFieldDto): void {
    // Kiểm tra type hợp lệ
    const validTypes = ['text', 'email', 'password', 'number', 'tel', 'date', 'datetime', 'time', 'select', 'checkbox', 'radio', 'textarea'];
    if (!validTypes.includes(createDto.type)) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
        `Loại trường '${createDto.type}' không hợp lệ. Phải là một trong các giá trị: ${validTypes.join(', ')}`,
      );
    }

    // Kiểm tra configJson hợp lệ
    this.validateConfigJson(createDto.configJson);

    // Kiểm tra userId phải được cung cấp
    this.validateUserProvided(createDto.userId);
  }

  /**
   * Kiểm tra tính hợp lệ của configJson
   * @param configJson Cấu hình JSON
   */
  private validateConfigJson(configJson: any): void {
    if (!configJson) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
        'Cấu hình JSON không được để trống',
      );
    }

    // Kiểm tra cấu trúc validation
    if (configJson.validation) {
      const { validation } = configJson;

      // Kiểm tra minLength và maxLength
      if (validation.minLength !== undefined && (typeof validation.minLength !== 'number' || validation.minLength < 0)) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
          'minLength phải là số không âm',
        );
      }

      if (validation.maxLength !== undefined && (typeof validation.maxLength !== 'number' || validation.maxLength < 0)) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
          'maxLength phải là số không âm',
        );
      }

      if (validation.minLength !== undefined && validation.maxLength !== undefined && validation.minLength > validation.maxLength) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
          'minLength không được lớn hơn maxLength',
        );
      }

      // Kiểm tra pattern
      if (validation.pattern !== undefined) {
        try {
          new RegExp(validation.pattern);
        } catch (error) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
            'pattern không phải là biểu thức chính quy hợp lệ',
          );
        }
      }

      // Kiểm tra minDate và maxDate
      if (validation.minDate !== undefined) {
        const minDate = new Date(validation.minDate);
        if (isNaN(minDate.getTime())) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
            'minDate không phải là ngày hợp lệ',
          );
        }
      }

      if (validation.maxDate !== undefined) {
        const maxDate = new Date(validation.maxDate);
        if (isNaN(maxDate.getTime())) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
            'maxDate không phải là ngày hợp lệ',
          );
        }
      }

      if (validation.minDate !== undefined && validation.maxDate !== undefined) {
        const minDate = new Date(validation.minDate);
        const maxDate = new Date(validation.maxDate);
        if (minDate > maxDate) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
            'minDate không được sau maxDate',
          );
        }
      }
    }

    // Kiểm tra options cho select và radio
    // Không cần kiểm tra component nữa, chỉ kiểm tra nếu có options thì phải hợp lệ
    if (configJson.options && !Array.isArray(configJson.options)) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
        'options phải là một mảng',
      );
    }
  }

  /**
   * Kiểm tra xem configId đã tồn tại cho cùng một người dùng chưa
   * @param existingField Trường tùy chỉnh đã tồn tại với configId
   * @param configId ID cấu hình cần kiểm tra
   * @param userId ID người dùng
   */
  validateConfigIdNotExists(existingField: CustomField | null, configId: string, userId: number | undefined): void {
    if (!userId) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_CREATION_FAILED,
        'ID người dùng không được để trống',
      );
    }

    if (existingField && existingField.userId === userId) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_CREATION_FAILED,
        `ID cấu hình '${configId}' đã tồn tại cho người dùng này`,
      );
    }
  }

  /**
   * Kiểm tra tính hợp lệ của dữ liệu cập nhật trường tùy chỉnh
   * @param updateDto DTO chứa thông tin cập nhật trường tùy chỉnh
   * @param existingField Trường tùy chỉnh hiện tại
   */
  validateUpdateCustomFieldData(updateDto: UpdateCustomFieldDto, existingField: CustomField): void {
    // Kiểm tra type hợp lệ nếu có cập nhật
    if (updateDto.type) {
      const validTypes = ['text', 'email', 'password', 'number', 'tel', 'date', 'datetime', 'time', 'select', 'checkbox', 'radio', 'textarea'];
      if (!validTypes.includes(updateDto.type)) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
          `Loại trường '${updateDto.type}' không hợp lệ. Phải là một trong các giá trị: ${validTypes.join(', ')}`,
        );
      }
    }

    // Kiểm tra configJson hợp lệ nếu có cập nhật
    if (updateDto.configJson) {
      this.validateConfigJson(updateDto.configJson);
    }
  }

  /**
   * Kiểm tra giá sản phẩm theo loại giá
   * @param price Giá sản phẩm
   * @param typePrice Loại giá
   * @param productType Loại sản phẩm (optional, để xử lý đặc biệt cho EVENT)
   * @throws AppException nếu giá không hợp lệ
   */
  validateProductPrice(price: any, typePrice: PriceTypeEnum, productType?: ProductTypeEnum): void {
    // Đối với EVENT products, cho phép bất kỳ combination nào vì sẽ được override
    if (productType === ProductTypeEnum.EVENT) {
      return; // Không validate cho EVENT products
    }

    // Kiểm tra giá theo loại giá cho các loại sản phẩm khác
    switch (typePrice) {
      case PriceTypeEnum.HAS_PRICE:
        // Kiểm tra có đủ các trường cần thiết không
        if (!price || !price.listPrice || !price.salePrice || !price.currency) {
          throw new AppException(
            BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
            'Với loại giá HAS_PRICE, cần cung cấp đầy đủ listPrice, salePrice và currency',
          );
        }

        // Kiểm tra giá bán phải nhỏ hơn hoặc bằng giá niêm yết
        if (price.salePrice > price.listPrice) {
          throw new AppException(
            BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
            'Giá bán (salePrice) phải nhỏ hơn hoặc bằng giá niêm yết (listPrice)',
          );
        }
        break;

      case PriceTypeEnum.STRING_PRICE:
        // Kiểm tra có trường priceDescription không
        if (!price || !price.priceDescription) {
          throw new AppException(
            BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
            'Với loại giá STRING_PRICE, cần cung cấp priceDescription',
          );
        }
        break;

      case PriceTypeEnum.NO_PRICE:
        // Kiểm tra price phải là null
        if (price !== null) {
          throw new AppException(
            BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
            'Với loại giá NO_PRICE, price phải là null',
          );
        }
        break;
    }
  }

  /**
   * Kiểm tra kho có tồn tại không
   * @param warehouseId ID của kho
   * @returns Thông tin kho nếu tồn tại
   * @throws AppException nếu kho không tồn tại
   */
  async validateWarehouseExists(warehouseId: number): Promise<Warehouse> {
    const warehouse = await this.warehouseRepository.findById_user(warehouseId);

    if (!warehouse) {
      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
        `Không tìm thấy kho với ID ${warehouseId}`,
      );
    }

    return warehouse;
  }

  /**
   * Kiểm tra trường tùy chỉnh của kho có tồn tại không - Logic đã bị xóa
   * WarehouseCustomField đã bị xóa hoàn toàn
   */
  /*
  async validateWarehouseCustomFieldExists(warehouseId: number, fieldId: number): Promise<WarehouseCustomField> {
    // Kiểm tra kho tồn tại
    await this.validateWarehouseExists(warehouseId);

    // Kiểm tra trường tùy chỉnh tồn tại
    const customField = await this.customFieldRepository.findByIdAndNotDeleted(fieldId);
    this.validateCustomFieldExists(customField, fieldId);

    // Kiểm tra trường tùy chỉnh của kho tồn tại
    const warehouseCustomField = await this.warehouseCustomFieldRepository.findByWarehouseIdAndFieldId_user(warehouseId, fieldId);

    if (!warehouseCustomField) {
      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_NOT_FOUND,
        `Không tìm thấy trường tùy chỉnh với ID ${fieldId} cho kho với ID ${warehouseId}`,
      );
    }

    return warehouseCustomField;
  }
  */

  /**
   * Kiểm tra tính hợp lệ của dữ liệu tạo trường tùy chỉnh cho kho - Logic đã bị xóa
   * WarehouseCustomField đã bị xóa hoàn toàn
   */
  /*
  async validateCreateWarehouseCustomField(warehouseId: number, createDto: CreateWarehouseCustomFieldDto): Promise<void> {
    // Kiểm tra kho tồn tại
    await this.validateWarehouseExists(warehouseId);

    // Kiểm tra trường tùy chỉnh tồn tại
    const customField = await this.customFieldRepository.findByIdAndNotDeleted(createDto.fieldId);
    this.validateCustomFieldExists(customField, createDto.fieldId);

    // Kiểm tra trường tùy chỉnh đã được thêm vào kho chưa
    const existingField = await this.warehouseCustomFieldRepository.findByWarehouseIdAndFieldId_user(warehouseId, createDto.fieldId);

    if (existingField) {
      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_ALREADY_EXISTS,
        `Trường tùy chỉnh với ID ${createDto.fieldId} đã tồn tại cho kho với ID ${warehouseId}`,
      );
    }
  }
  */

  /**
   * Kiểm tra tính hợp lệ của dữ liệu tạo kho mới
   * @param createDto DTO chứa thông tin tạo kho mới
   * @throws AppException nếu dữ liệu không hợp lệ
   */
  async validateCreateWarehouse(createDto: CreateWarehouseDto): Promise<void> {
    // Kiểm tra tên kho đã tồn tại chưa
    const existingWarehouse = await this.warehouseRepository.findByName(createDto.name);
    if (existingWarehouse) {
      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_CREATION_FAILED,
        `Kho với tên '${createDto.name}' đã tồn tại`,
      );
    }

    // Kiểm tra loại kho hợp lệ
    if (!Object.values(WarehouseTypeEnum).includes(createDto.type)) {
      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_CREATION_FAILED,
        `Loại kho '${createDto.type}' không hợp lệ`,
      );
    }
  }

  /**
   * Kiểm tra tính hợp lệ của dữ liệu cập nhật kho
   * @param warehouseId ID của kho cần cập nhật
   * @param updateDto DTO chứa thông tin cập nhật kho
   * @throws AppException nếu dữ liệu không hợp lệ
   */
  async validateUpdateWarehouse(warehouseId: number, updateDto: UpdateWarehouseDto): Promise<void> {
    // Kiểm tra kho tồn tại
    await this.validateWarehouseExists(warehouseId);

    // Kiểm tra tên kho đã tồn tại chưa nếu có cập nhật tên
    if (updateDto.name) {
      const existingWarehouse = await this.warehouseRepository.findByName(updateDto.name);
      if (existingWarehouse && existingWarehouse.warehouseId !== warehouseId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.WAREHOUSE_UPDATE_FAILED,
          `Kho với tên '${updateDto.name}' đã tồn tại`,
        );
      }
    }

    // Kiểm tra loại kho hợp lệ nếu có cập nhật loại
    if (updateDto.type && !Object.values(WarehouseTypeEnum).includes(updateDto.type)) {
      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_UPDATE_FAILED,
        `Loại kho '${updateDto.type}' không hợp lệ`,
      );
    }
  }

  /**
   * Kiểm tra kho ảo có tồn tại không
   * @param warehouseId ID của kho
   * @returns Thông tin kho ảo nếu tồn tại
   * @throws AppException nếu kho ảo không tồn tại
   */
  async validateVirtualWarehouseExists(warehouseId: number): Promise<VirtualWarehouse> {
    // Kiểm tra kho chung tồn tại
    await this.validateWarehouseExists(warehouseId);

    // Kiểm tra kho ảo tồn tại
    const virtualWarehouse = await this.virtualWarehouseRepository.findByWarehouseId_user(warehouseId);

    if (!virtualWarehouse) {
      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
        `Không tìm thấy kho ảo với ID ${warehouseId}`,
      );
    }

    return virtualWarehouse;
  }

  /**
   * Kiểm tra tính hợp lệ của dữ liệu tạo kho ảo mới
   * @param createDto DTO chứa thông tin tạo kho ảo mới
   * @param warehouseId ID của kho chung
   * @throws AppException nếu dữ liệu không hợp lệ
   */
  async validateCreateVirtualWarehouse(createDto: CreateVirtualWarehouseDto, warehouseId: number): Promise<void> {
    // Kiểm tra kho chung tồn tại
    const warehouse = await this.validateWarehouseExists(warehouseId);

    // Kiểm tra loại kho phải là VIRTUAL
    if (warehouse.type !== WarehouseTypeEnum.VIRTUAL) {
      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_TYPE_MISMATCH,
        `Kho với ID ${warehouseId} không phải là kho ảo`,
      );
    }

    // Kiểm tra kho ảo đã tồn tại chưa
    const existingVirtualWarehouse = await this.virtualWarehouseRepository.findByWarehouseId_user(warehouseId);
    if (existingVirtualWarehouse) {
      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_ALREADY_EXISTS,
        `Kho ảo với ID ${warehouseId} đã tồn tại`,
      );
    }
  }

  /**
   * Kiểm tra tính hợp lệ của dữ liệu cập nhật kho ảo
   * @param warehouseId ID của kho ảo cần cập nhật
   * @param updateDto DTO chứa thông tin cập nhật kho ảo
   * @throws AppException nếu dữ liệu không hợp lệ
   */
  async validateUpdateVirtualWarehouse(warehouseId: number, updateDto: UpdateVirtualWarehouseDto): Promise<void> {
    // Kiểm tra kho ảo tồn tại
    await this.validateVirtualWarehouseExists(warehouseId);
  }

  /**
   * Kiểm tra file có tồn tại không
   * @param id ID của file
   * @returns Thông tin file nếu tồn tại
   * @throws AppException nếu file không tồn tại
   */
  async validateFileExists(id: number): Promise<File> {
    const file = await this.fileRepository.findByIdUser(id);

    if (!file) {
      throw new AppException(
        BUSINESS_ERROR_CODES.FILE_NOT_FOUND,
        `Không tìm thấy file với ID ${id}`,
      );
    }

    return file;
  }

  /**
   * Kiểm tra tính hợp lệ của dữ liệu tạo file mới
   * @param createDto DTO chứa thông tin tạo file mới
   * @throws AppException nếu dữ liệu không hợp lệ
   */
  async validateCreateFile(createDto: CreateFileDto): Promise<void> {
    // Kiểm tra thư mục tồn tại nếu có folderId
    if (createDto.folderId) {
      await this.validateFolderExists(createDto.folderId);
    }
  }

  /**
   * Kiểm tra tính hợp lệ của dữ liệu cập nhật file
   * @param id ID của file cần cập nhật
   * @param updateDto DTO chứa thông tin cập nhật file
   * @throws AppException nếu dữ liệu không hợp lệ
   */
  async validateUpdateFile(id: number, updateDto: UpdateFileDto): Promise<void> {
    // Kiểm tra file tồn tại
    const file = await this.validateFileExists(id);

    // Kiểm tra thư mục tồn tại nếu có cập nhật folderId
    if (updateDto.folderId !== undefined) {
      // Không cho phép đặt folderId thành null vì folder_id là NOT NULL trong database
      if (updateDto.folderId === null) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'ID thư mục không được để trống',
        );
      } else {
        // Kiểm tra thư mục tồn tại
        await this.validateFolderExists(updateDto.folderId);
      }
    }
  }

  /**
   * Kiểm tra thư mục có tồn tại không
   * @param id ID của thư mục
   * @returns Thông tin thư mục nếu tồn tại
   * @throws AppException nếu thư mục không tồn tại
   */
  async validateFolderExists(id: number): Promise<Folder> {
    const folder = await this.folderRepository.findById_user(id);

    if (!folder) {
      throw new AppException(
        BUSINESS_ERROR_CODES.FOLDER_NOT_FOUND,
        `Không tìm thấy thư mục với ID ${id}`,
      );
    }

    return folder;
  }

  /**
   * Kiểm tra tính hợp lệ của dữ liệu tạo thư mục mới
   * @param createDto DTO chứa thông tin tạo thư mục mới
   * @param userId ID của người dùng
   * @throws AppException nếu dữ liệu không hợp lệ
   */
  async validateCreateFolder(createDto: CreateFolderDto, userId: number): Promise<void> {
    // Kiểm tra thư mục cha tồn tại nếu có
    if (createDto.parentId) {
      const parentFolder = await this.validateFolderExists(createDto.parentId);

      // Kiểm tra thư mục cha có thuộc về người dùng không
      if (parentFolder.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FOLDER_CREATION_FAILED,
          `Thư mục cha với ID ${createDto.parentId} không thuộc về người dùng`,
        );
      }

      // Kiểm tra trùng lặp tên thư mục trong cùng thư mục cha
      const existingFolder = await this.folderRepository.findByParentIdAndName(
        createDto.parentId,
        createDto.name,
        userId
      );

      if (existingFolder) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FOLDER_CREATION_FAILED,
          `Thư mục với tên '${createDto.name}' đã tồn tại trong thư mục cha này`,
        );
      }
    } else {
      // Kiểm tra trùng lặp tên thư mục gốc
      const existingFolder = await this.folderRepository.findByParentIdAndName(
        null,
        createDto.name,
        userId
      );

      if (existingFolder) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FOLDER_CREATION_FAILED,
          `Thư mục gốc với tên '${createDto.name}' đã tồn tại`,
        );
      }
    }

    // Kiểm tra kho ảo tồn tại nếu có root (ID kho ảo)
    if (createDto.root) {
      try {
        // Kiểm tra kho ảo tồn tại
        await this.validateVirtualWarehouseExists(createDto.root);
      } catch (error) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FOLDER_CREATION_FAILED,
          `Không thể tạo thư mục: Kho ảo với ID ${createDto.root} không tồn tại hoặc không hợp lệ`,
        );
      }
    } else {
      throw new AppException(
        BUSINESS_ERROR_CODES.FOLDER_CREATION_FAILED,
        `Không thể tạo thư mục: Cần cung cấp ID kho ảo (root)`,
      );
    }
  }

  /**
   * Kiểm tra tính hợp lệ của dữ liệu cập nhật thư mục
   * @param id ID của thư mục cần cập nhật
   * @param updateDto DTO chứa thông tin cập nhật thư mục
   * @param userId ID của người dùng
   * @throws AppException nếu dữ liệu không hợp lệ
   */
  async validateUpdateFolder(id: number, updateDto: UpdateFolderDto, userId: number): Promise<void> {
    // Kiểm tra thư mục tồn tại
    const folder = await this.validateFolderExists(id);

    // Kiểm tra thư mục có thuộc về người dùng không
    if (folder.userId !== userId) {
      throw new AppException(
        BUSINESS_ERROR_CODES.FOLDER_UPDATE_FAILED,
        `Thư mục với ID ${id} không thuộc về người dùng`,
      );
    }

    // Kiểm tra trùng lặp tên thư mục nếu có cập nhật tên
    if (updateDto.name) {
      // Xác định parentId của thư mục (giữ nguyên hoặc cập nhật)
      const parentId = updateDto.parentId !== undefined ? updateDto.parentId : folder.parentId;

      // Kiểm tra trùng lặp tên thư mục trong cùng thư mục cha
      const existingFolder = await this.folderRepository.findByParentIdAndName(
        parentId,
        updateDto.name,
        userId
      );

      // Nếu tìm thấy thư mục trùng tên và không phải là chính thư mục đang cập nhật
      if (existingFolder && existingFolder.id !== id) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FOLDER_UPDATE_FAILED,
          `Thư mục với tên '${updateDto.name}' đã tồn tại trong thư mục cha này`,
        );
      }
    }

    // Kiểm tra thư mục cha tồn tại nếu có cập nhật parentId
    if (updateDto.parentId !== undefined && updateDto.parentId !== null) {
      // Kiểm tra không được chọn chính nó làm thư mục cha
      if (updateDto.parentId === id) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FOLDER_UPDATE_FAILED,
          'Không thể chọn chính thư mục này làm thư mục cha',
        );
      }

      const parentFolder = await this.validateFolderExists(updateDto.parentId);

      // Kiểm tra thư mục cha có thuộc về người dùng không
      if (parentFolder.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FOLDER_UPDATE_FAILED,
          `Thư mục cha với ID ${updateDto.parentId} không thuộc về người dùng`,
        );
      }

      // Nếu có cập nhật parentId nhưng không cập nhật tên, cũng cần kiểm tra trùng lặp tên
      if (!updateDto.name) {
        const existingFolder = await this.folderRepository.findByParentIdAndName(
          updateDto.parentId,
          folder.name,
          userId
        );

        if (existingFolder && existingFolder.id !== id) {
          throw new AppException(
            BUSINESS_ERROR_CODES.FOLDER_UPDATE_FAILED,
            `Thư mục với tên '${folder.name}' đã tồn tại trong thư mục cha mới`,
          );
        }
      }
    }

    // Kiểm tra kho ảo tồn tại nếu có cập nhật root
    if (updateDto.root !== undefined && updateDto.root !== null) {
      try {
        // Kiểm tra kho ảo tồn tại
        await this.validateVirtualWarehouseExists(updateDto.root);
      } catch (error) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FOLDER_UPDATE_FAILED,
          `Không thể cập nhật thư mục: Kho ảo với ID ${updateDto.root} không tồn tại hoặc không hợp lệ`,
        );
      }
    } else if (updateDto.root === null) {
      throw new AppException(
        BUSINESS_ERROR_CODES.FOLDER_UPDATE_FAILED,
        `Không thể cập nhật thư mục: Không thể đặt ID kho ảo (root) thành null`,
      );
    }
  }

  /**
   * Kiểm tra tồn kho có tồn tại không
   * @param inventory Tồn kho cần kiểm tra
   * @param id ID của tồn kho
   * @throws AppException nếu tồn kho không tồn tại
   */
  validateInventoryExists(inventory: Inventory | null, id: number): asserts inventory is Inventory {
    if (!inventory) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_NOT_FOUND,
        `Không tìm thấy tồn kho với ID ${id}`,
      );
    }
  }

  /**
   * Kiểm tra tính hợp lệ của dữ liệu tạo tồn kho mới
   * @param createDto DTO chứa thông tin tạo tồn kho mới
   * @param userId ID của người dùng hiện tại
   * @throws AppException nếu dữ liệu không hợp lệ
   */
  async validateCreateInventory(createDto: CreateInventoryDto, userId: number): Promise<void> {
    // Kiểm tra sản phẩm tồn tại
    const product = await this.userProductRepository.findOne({ where: { id: createDto.productId } });
    if (!product) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_CREATION_FAILED,
        `Không tìm thấy sản phẩm với ID ${createDto.productId}`,
      );
    }

    // Kiểm tra kho tồn tại
    const warehouse = await this.warehouseRepository.findById_user(createDto.warehouseId);
    if (!warehouse) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_CREATION_FAILED,
        `Không tìm thấy kho với ID ${createDto.warehouseId}`,
      );
    }

    // Kiểm tra sản phẩm đã có trong kho chưa
    const existingInventory = await this.inventoryRepository.findByProductAndWarehouse(
      createDto.productId,
      createDto.warehouseId
    );
    if (existingInventory) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_CREATION_FAILED,
        `Sản phẩm với ID ${createDto.productId} đã tồn tại trong kho với ID ${createDto.warehouseId}`,
      );
    }

    // Kiểm tra SKU không được trùng với sản phẩm của user đang đăng nhập
    if (createDto.sku) {
      await this.validateSkuUniqueness(createDto.sku, userId);
    }

    // Tính toán và cập nhật số lượng
    this.calculateInventoryQuantities(createDto);
  }

  /**
   * Kiểm tra SKU không được trùng với sản phẩm của user đang đăng nhập
   * @param sku Mã SKU cần kiểm tra
   * @param userId ID của người dùng hiện tại
   * @throws AppException nếu SKU đã tồn tại
   */
  async validateSkuUniqueness(sku: string, userId: number): Promise<void> {
    const existingInventory = await this.inventoryRepository.findBySkuAndUserId(sku, userId);
    if (existingInventory) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_CREATION_FAILED,
        `Mã SKU "${sku}" đã tồn tại trong sản phẩm khác của bạn`,
      );
    }
  }

  /**
   * Kiểm tra tính hợp lệ của dữ liệu cập nhật tồn kho
   * @param id ID của tồn kho
   * @param updateDto DTO chứa thông tin cập nhật tồn kho
   * @throws AppException nếu dữ liệu không hợp lệ
   */
  async validateUpdateInventory(id: number, updateDto: UpdateInventoryDto): Promise<void> {
    // Kiểm tra tồn kho tồn tại
    const inventory = await this.inventoryRepository.findById(id);
    this.validateInventoryExists(inventory, id);

    // Kiểm tra kho tồn tại nếu có cập nhật warehouseId
    if (updateDto.warehouseId) {
      const warehouse = await this.warehouseRepository.findById_user(updateDto.warehouseId);
      if (!warehouse) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVENTORY_UPDATE_FAILED,
          `Không tìm thấy kho với ID ${updateDto.warehouseId}`,
        );
      }
    }

    // Tính toán và cập nhật số lượng
    this.calculateInventoryQuantities(updateDto);
  }

  /**
   * Tính toán và cập nhật các số lượng trong tồn kho
   * @param data Dữ liệu chứa các số lượng
   * @returns Dữ liệu đã được cập nhật
   * @throws AppException nếu số lượng không hợp lệ
   */
  calculateInventoryQuantities(data: Partial<Inventory>): Partial<Inventory> {
    // Đảm bảo availableQuantity không âm
    if (data.availableQuantity !== undefined && data.availableQuantity < 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_VALIDATION_FAILED,
        'Số lượng sẵn sàng không được nhỏ hơn 0',
      );
    }

    // Loại bỏ các trường tính toán tự động nếu người dùng gửi lên
    delete data.currentQuantity;
    delete data.totalQuantity;

    // Đặt reservedQuantity và defectiveQuantity về 0 mặc định
    data.reservedQuantity = 0;
    data.defectiveQuantity = 0;

    // Lấy availableQuantity, mặc định là 0 nếu không được cung cấp
    const availableQty = data.availableQuantity !== undefined ? data.availableQuantity : 0;

    // Tính toán số lượng hiện tại = availableQuantity (vì reserved và defective = 0)
    data.currentQuantity = availableQty;

    // Tổng số lượng = availableQuantity (đây cũng là total)
    data.totalQuantity = availableQty;

    return data;
  }

  /**
   * Validate metadata fields against custom field configurations
   * @param metadata Array of metadata fields to validate
   * @param customFields Array of custom field configurations
   * @param userId User ID for context
   * @throws AppException if validation fails
   */
  validateMetadataFields(
    metadata: MetadataFieldDto[],
    customFields: CustomField[],
    userId: number
  ): void {
    if (!metadata || metadata.length === 0) {
      // Check if there are any required custom fields
      const requiredFields = customFields.filter(field => field.required && field.userId === userId);
      if (requiredFields.length > 0) {
        const requiredConfigIds = requiredFields.map(field => field.configId).join(', ');
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
          `Các trường bắt buộc sau đây chưa được điền: ${requiredConfigIds}`
        );
      }
      return;
    }

    // Create a map of custom fields by configId for quick lookup
    // Include both user-created fields and employee-created fields
    const customFieldMap = new Map<string, CustomField>();
    customFields.forEach(field => customFieldMap.set(field.configId, field));

    // Validate each metadata field
    for (const metadataField of metadata) {
      const customField = customFieldMap.get(metadataField.configId);

      if (!customField) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_NOT_FOUND,
          `Không tìm thấy cấu hình cho trường tùy chỉnh với configId: ${metadataField.configId}`
        );
      }

      // Validate required fields
      if (customField.required && (metadataField.value === undefined || metadataField.value === null || metadataField.value === '')) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
          `Trường '${customField.label}' là bắt buộc và không được để trống`
        );
      }

      // Validate field value based on type and configJson
      this.validateMetadataFieldValue(metadataField, customField);
    }

    // Note: Removed the check for missing required fields
    // Now we only validate the fields that are actually provided in metadata
    // This allows users to create convert customers with partial custom field data
  }

  /**
   * Validate a single metadata field value against its custom field configuration
   * @param metadataField The metadata field to validate
   * @param customField The custom field configuration
   * @throws AppException if validation fails
   */
  private validateMetadataFieldValue(
    metadataField: MetadataFieldDto,
    customField: CustomField
  ): void {
    const { value } = metadataField;
    const { type, configJson, label } = customField;

    // Skip validation if value is empty and field is not required
    if (!customField.required && (value === undefined || value === null || value === '')) {
      return;
    }

    // Type-specific validation
    switch (type) {
      case 'text':
      case 'textarea':
        this.validateTextValue(value, configJson, label);
        break;
      case 'number':
        this.validateNumberValue(value, configJson, label);
        break;
      case 'email':
        this.validateEmailValue(value, configJson, label);
        break;
      case 'phone':
        this.validatePhoneValue(value, configJson, label);
        break;
      case 'select':
        this.validateSelectValue(value, configJson, label);
        break;
      case 'multiselect':
        this.validateMultiselectValue(value, configJson, label);
        break;
      case 'date':
      case 'datetime':
        this.validateDateValue(value, configJson, label);
        break;
      case 'checkbox':
        this.validateCheckboxValue(value, configJson, label);
        break;
      case 'radio':
        this.validateRadioValue(value, configJson, label);
        break;
      default:
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
          `Loại trường '${type}' không được hỗ trợ cho trường '${label}'`
        );
    }
  }

  /**
   * Validate text/textarea field value
   */
  private validateTextValue(value: any, configJson: any, label: string): void {
    if (typeof value !== 'string') {
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
        `Trường '${label}' phải là chuỗi văn bản`
      );
    }

    const validation = configJson?.validation || {};

    if (validation.minLength && value.length < validation.minLength) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
        `Trường '${label}' phải có ít nhất ${validation.minLength} ký tự`
      );
    }

    if (validation.maxLength && value.length > validation.maxLength) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
        `Trường '${label}' không được vượt quá ${validation.maxLength} ký tự`
      );
    }

    if (validation.pattern) {
      const regex = new RegExp(validation.pattern);
      if (!regex.test(value)) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
          `Trường '${label}' không đúng định dạng yêu cầu`
        );
      }
    }
  }

  /**
   * Validate number field value
   */
  private validateNumberValue(value: any, configJson: any, label: string): void {
    const numValue = typeof value === 'string' ? parseFloat(value) : value;

    if (typeof numValue !== 'number' || isNaN(numValue)) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
        `Trường '${label}' phải là số hợp lệ`
      );
    }

    const validation = configJson?.validation || {};

    if (validation.min !== undefined && numValue < validation.min) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
        `Trường '${label}' phải lớn hơn hoặc bằng ${validation.min}`
      );
    }

    if (validation.max !== undefined && numValue > validation.max) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
        `Trường '${label}' phải nhỏ hơn hoặc bằng ${validation.max}`
      );
    }
  }

  /**
   * Validate email field value
   */
  private validateEmailValue(value: any, configJson: any, label: string): void {
    if (typeof value !== 'string') {
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
        `Trường '${label}' phải là chuỗi văn bản`
      );
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
        `Trường '${label}' phải là địa chỉ email hợp lệ`
      );
    }
  }

  /**
   * Validate phone field value
   */
  private validatePhoneValue(value: any, configJson: any, label: string): void {
    if (typeof value !== 'string') {
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
        `Trường '${label}' phải là chuỗi văn bản`
      );
    }

    // Vietnamese phone number validation
    const phoneRegex = /^(0|\+84)[3|5|7|8|9][0-9]{8}$/;
    if (!phoneRegex.test(value)) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
        `Trường '${label}' phải là số điện thoại Việt Nam hợp lệ`
      );
    }
  }

  /**
   * Validate select field value
   */
  private validateSelectValue(value: any, configJson: any, label: string): void {
    const options = configJson?.options || [];

    if (!Array.isArray(options) || options.length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
        `Trường '${label}' không có tùy chọn nào được cấu hình`
      );
    }

    const validValues = options.map(option => option.value);
    if (!validValues.includes(value)) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
        `Trường '${label}' phải chọn một trong các giá trị: ${validValues.join(', ')}`
      );
    }
  }

  /**
   * Validate multiselect field value
   */
  private validateMultiselectValue(value: any, configJson: any, label: string): void {
    if (!Array.isArray(value)) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
        `Trường '${label}' phải là mảng các giá trị`
      );
    }

    const options = configJson?.options || [];

    if (!Array.isArray(options) || options.length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
        `Trường '${label}' không có tùy chọn nào được cấu hình`
      );
    }

    const validValues = options.map(option => option.value);
    const invalidValues = value.filter(v => !validValues.includes(v));

    if (invalidValues.length > 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
        `Trường '${label}' chứa các giá trị không hợp lệ: ${invalidValues.join(', ')}`
      );
    }
  }

  /**
   * Validate date/datetime field value
   */
  private validateDateValue(value: any, configJson: any, label: string): void {
    const date = new Date(value);

    if (isNaN(date.getTime())) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
        `Trường '${label}' phải là ngày tháng hợp lệ`
      );
    }

    const validation = configJson?.validation || {};

    if (validation.minDate) {
      const minDate = new Date(validation.minDate);
      if (date < minDate) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
          `Trường '${label}' phải sau ngày ${minDate.toLocaleDateString()}`
        );
      }
    }

    if (validation.maxDate) {
      const maxDate = new Date(validation.maxDate);
      if (date > maxDate) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
          `Trường '${label}' phải trước ngày ${maxDate.toLocaleDateString()}`
        );
      }
    }
  }

  /**
   * Validate checkbox field value
   */
  private validateCheckboxValue(value: any, configJson: any, label: string): void {
    if (typeof value !== 'boolean') {
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
        `Trường '${label}' phải là giá trị true hoặc false`
      );
    }
  }

  /**
   * Validate radio field value
   */
  private validateRadioValue(value: any, configJson: any, label: string): void {
    this.validateSelectValue(value, configJson, label);
  }
}
