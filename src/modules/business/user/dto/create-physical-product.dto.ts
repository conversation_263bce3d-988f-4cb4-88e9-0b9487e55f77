import { ApiProperty, OmitType } from '@nestjs/swagger';
import { BusinessCreateProductDto } from './create-product.dto';
import { ProductTypeEnum } from '@modules/business/enums';

/**
 * DTO cho việc tạo sản phẩm vật lý
 */
export class CreatePhysicalProductDto extends OmitType(BusinessCreateProductDto, ['advancedInfo'] as const) {
  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
    default: ProductTypeEnum.PHYSICAL,
  })
  productType: ProductTypeEnum.PHYSICAL;
}
