import { ApiProperty, ApiExtraModels } from '@nestjs/swagger';
import { Type, Transform } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  Validate,
  Allow,
  ValidationArguments,
} from 'class-validator';
import { PriceTypeEnum, ProductTypeEnum } from '@modules/business/enums';
import { CreateClassificationDto } from './classification.dto';
import { CustomFieldInputDto } from './custom-field-metadata.dto';
import { ProductInventoryDto } from './product-inventory.dto';
import { DigitalProductDto } from './digital-product.dto';
import { EventProductDto } from './event-product.dto';
import { ServiceProductDto } from './service-product.dto';
import { IsValidAdvancedInfo } from '../validators/product-advanced-info.validator';

// Đã loại bỏ CustomFieldValueDto vì không còn cần thiết

/**
 * Custom validator để kiểm tra price dựa trên typePrice
 */
@ValidatorConstraint({ name: 'priceValidation', async: false })
export class PriceValidationConstraint implements ValidatorConstraintInterface {
  validate(price: any, args: any) {
    const object = args.object as BusinessCreateProductDto;
    const typePrice = object.typePrice;

    // Nếu typePrice chưa được set hoặc undefined, bỏ qua validation
    // Điều này xảy ra khi validation chạy trước khi typePrice được transform
    if (!typePrice) {
      return true;
    }

    switch (typePrice) {
      case PriceTypeEnum.NO_PRICE:
        // Với NO_PRICE, price phải là null hoặc undefined
        return price === null || price === undefined;
      case PriceTypeEnum.HAS_PRICE:
        // Với HAS_PRICE, price phải có đầy đủ thông tin
        return price &&
               typeof price === 'object' &&
               typeof price.listPrice === 'number' &&
               typeof price.salePrice === 'number' &&
               typeof price.currency === 'string' &&
               price.listPrice > 0 &&
               price.salePrice > 0;
      case PriceTypeEnum.STRING_PRICE:
        // Với STRING_PRICE, price phải có priceDescription
        return price &&
               typeof price === 'object' &&
               typeof price.priceDescription === 'string' &&
               price.priceDescription.trim().length > 0;
      default:
        return false;
    }
  }

  defaultMessage(args: any) {
    const object = args.object as BusinessCreateProductDto;
    const typePrice = object.typePrice;

    if (!typePrice) {
      return 'TypePrice is required for price validation';
    }

    switch (typePrice) {
      case PriceTypeEnum.NO_PRICE:
        return 'Price must be null when typePrice is NO_PRICE';
      case PriceTypeEnum.HAS_PRICE:
        return 'Price must have valid listPrice, salePrice and currency when typePrice is HAS_PRICE';
      case PriceTypeEnum.STRING_PRICE:
        return 'Price must have valid priceDescription when typePrice is STRING_PRICE';
      default:
        return 'Invalid price for the given typePrice';
    }
  }
}

/**
 * DTO cho cấu hình vận chuyển trong module business
 */
export class BusinessShipmentConfigDto {
  @ApiProperty({
    description: 'Chiều rộng (cm)',
    example: 25,
  })
  @IsNumber()
  @IsOptional()
  widthCm?: number;

  @ApiProperty({
    description: 'Chiều cao (cm)',
    example: 5,
  })
  @IsNumber()
  @IsOptional()
  heightCm?: number;

  @ApiProperty({
    description: 'Chiều dài (cm)',
    example: 30,
  })
  @IsNumber()
  @IsOptional()
  lengthCm?: number;

  @ApiProperty({
    description: 'Trọng lượng (gram)',
    example: 200,
  })
  @IsNumber()
  @IsOptional()
  weightGram?: number;
}

/**
 * DTO cho giá sản phẩm khi typePrice là HAS_PRICE
 */
export class HasPriceDto {
  @ApiProperty({
    description: 'Giá niêm yết',
    example: 200000,
  })
  @IsNumber()
  @IsNotEmpty()
  listPrice: number;

  @ApiProperty({
    description: 'Giá bán',
    example: 150000,
  })
  @IsNumber()
  @IsNotEmpty()
  salePrice: number;

  @ApiProperty({
    description: 'Đơn vị tiền tệ',
    example: 'VND',
  })
  @IsString()
  @IsNotEmpty()
  currency: string;
}

/**
 * DTO cho giá sản phẩm khi typePrice là STRING_PRICE
 */
export class StringPriceDto {
  @ApiProperty({
    description: 'Mô tả giá',
    example: 'Giá chưa công bố',
  })
  @IsString()
  @IsNotEmpty()
  priceDescription: string;
}

/**
 * DTO cho việc tạo sản phẩm mới trong module business
 */
@ApiExtraModels(DigitalProductDto, EventProductDto, ServiceProductDto, HasPriceDto, StringPriceDto)
export class BusinessCreateProductDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Áo thun nam',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
  })
  @IsEnum(ProductTypeEnum)
  @IsNotEmpty()
  productType: ProductTypeEnum;

  @ApiProperty({
    description: 'Loại giá (Optional cho EVENT products - sẽ tự động set NO_PRICE)',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    required: false,
  })
  @IsOptional()
  @IsEnum(PriceTypeEnum)
  typePrice?: PriceTypeEnum;

  @ApiProperty({
    description: 'Giá sản phẩm (Optional cho EVENT products - giá được lấy từ ticket types)',
    oneOf: [
      { $ref: '#/components/schemas/HasPriceDto' },
      { $ref: '#/components/schemas/StringPriceDto' },
      { type: 'null' }
    ],
    example: {
      listPrice: 200000,
      salePrice: 150000,
      currency: 'VND'
    },
    required: false,
  })
  @IsOptional()
  @Validate(PriceValidationConstraint)
  price?: HasPriceDto | StringPriceDto | null;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Áo thun nam chất liệu cotton cao cấp',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Danh sách loại media cho hình ảnh sản phẩm',
    type: [String],
    example: ['image/jpeg', 'image/png'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  imagesMediaTypes?: string[];

  @ApiProperty({
    description: 'Danh sách tag',
    type: [String],
    example: ['áo thun', 'nam', 'cotton'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    description: 'Danh sách custom fields cho sản phẩm',
    type: [CustomFieldInputDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldInputDto)
  customFields?: CustomFieldInputDto[];

  @ApiProperty({
    description: 'Cấu hình vận chuyển',
    type: BusinessShipmentConfigDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => BusinessShipmentConfigDto)
  shipmentConfig?: BusinessShipmentConfigDto;

  @ApiProperty({
    description: 'Danh sách phân loại sản phẩm',
    type: [CreateClassificationDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateClassificationDto)
  classifications?: CreateClassificationDto[];

  @ApiProperty({
    description: 'Thông tin tồn kho sản phẩm (sẽ được tạo cùng với sản phẩm)',
    type: ProductInventoryDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductInventoryDto)
  inventory?: ProductInventoryDto;

  @ApiProperty({
    description: 'Thông tin nâng cao cho sản phẩm (chỉ áp dụng cho DIGITAL, EVENT, SERVICE)',
    oneOf: [
      { $ref: '#/components/schemas/DigitalProductDto' },
      { $ref: '#/components/schemas/EventProductDto' },
      { $ref: '#/components/schemas/ServiceProductDto' }
    ],
    example: {
      // Ví dụ cho DIGITAL
      purchaseCount: 0,
      digitalFulfillmentFlow: {
        deliveryMethod: 'dashboard_download',
        deliveryTiming: 'immediate',
        deliveryDelayMinutes: 0,
        accessStatus: 'pending'
      },
      digitalOutput: {
        outputType: 'online_course',
        accessLink: 'https://course.example.com/activate?token=abc123',
        loginInfo: {
          username: 'auto_generated',
          password: 'temp_password'
        },
        usageInstructions: 'Vui lòng đăng nhập bằng thông tin được cung cấp để truy cập khóa học'
      }
    }
  })
  @IsOptional()
  @Allow()
  @IsValidAdvancedInfo()
  advancedInfo?: any;
}

/**
 * DTO cho việc tạo nhiều sản phẩm cùng lúc
 */
export class BusinessBatchCreateProductDto {
  @ApiProperty({
    description: 'Danh sách sản phẩm cần tạo',
    type: [BusinessCreateProductDto],
    example: [
      {
        name: 'Áo thun nam basic',
        price: {
          listPrice: 200000,
          salePrice: 150000,
          currency: 'VND'
        },
        typePrice: 'HAS_PRICE',
        description: 'Áo thun nam chất liệu cotton cao cấp, form regular fit',
        imagesMediaTypes: ['image/jpeg', 'image/png'],
        tags: ['áo thun', 'nam', 'cotton', 'basic'],
        customFields: [
          {
            customFieldId: 90,
            value: {
              value: 'XL'
            }
          },
          {
            customFieldId: 91,
            value: {
              value: '2024-12-31'
            }
          }
        ],
        shipmentConfig: {
          widthCm: 25,
          heightCm: 5,
          lengthCm: 30,
          weightGram: 200
        },
        classifications: [
          {
            type: 'Màu sắc',
            price: {
              listPrice: 200000,
              salePrice: 150000,
              currency: 'VND'
            },
            customFields: [
              {
                label: 'Màu sắc',
                type: 'select',
                required: true,
                value: {
                  value: 'Đỏ'
                }
              },
              {
                label: 'Độ bền màu',
                type: 'text',
                required: false,
                value: {
                  value: 'Cao'
                }
              }
            ]
          },
          {
            type: 'Kích thước',
            price: {
              listPrice: 220000,
              salePrice: 170000,
              currency: 'VND'
            },
            customFields: [
              {
                label: 'Size',
                type: 'select',
                required: true,
                value: {
                  value: 'XL'
                }
              },
              {
                label: 'Chiều dài áo',
                type: 'number',
                required: false,
                value: {
                  value: '72'
                }
              }
            ]
          }
        ],
        inventory: {
          warehouseId: 1,
          availableQuantity: 100,
          reservedQuantity: 10,
          defectiveQuantity: 5,
          sku: 'SHIRT-001',
          barcode: '1234567890123'
        }
      },
      {
        name: 'Quần jean nữ skinny',
        price: {
          listPrice: 450000,
          salePrice: 350000,
          currency: 'VND'
        },
        typePrice: 'HAS_PRICE',
        description: 'Quần jean nữ form skinny, chất liệu denim cao cấp',
        imagesMediaTypes: ['image/jpeg', 'image/webp'],
        tags: ['quần jean', 'nữ', 'skinny', 'denim'],
        customFields: [
          {
            customFieldId: 92,
            value: {
              value: 'M'
            }
          },
          {
            customFieldId: 93,
            value: {
              value: 'Việt Nam'
            }
          }
        ],
        shipmentConfig: {
          widthCm: 30,
          heightCm: 8,
          lengthCm: 35,
          weightGram: 400
        },
        classifications: [
          {
            type: 'Màu sắc',
            price: {
              listPrice: 450000,
              salePrice: 350000,
              currency: 'VND'
            },
            customFields: [
              {
                label: 'Màu chủ đạo',
                type: 'select',
                required: true,
                value: {
                  value: 'Xanh đậm'
                }
              },
              {
                label: 'Hiệu ứng',
                type: 'text',
                required: false,
                value: {
                  value: 'Rách nhẹ'
                }
              }
            ]
          },
          {
            type: 'Kích thước',
            price: {
              listPrice: 450000,
              salePrice: 350000,
              currency: 'VND'
            },
            customFields: [
              {
                label: 'Size',
                type: 'select',
                required: true,
                value: {
                  value: '29'
                }
              },
              {
                label: 'Chiều dài',
                type: 'number',
                required: true,
                value: {
                  value: '95'
                }
              }
            ]
          },
          {
            type: 'Phong cách',
            price: {
              listPrice: 480000,
              salePrice: 380000,
              currency: 'VND'
            },
            customFields: [
              {
                label: 'Kiểu dáng',
                type: 'select',
                required: true,
                value: {
                  value: 'Skinny fit'
                }
              },
              {
                label: 'Độ co giãn',
                type: 'text',
                required: false,
                value: {
                  value: 'Trung bình'
                }
              }
            ]
          }
        ]
      },
      {
        name: 'Giày sneaker unisex',
        price: {
          listPrice: 800000,
          salePrice: 650000,
          currency: 'VND'
        },
        typePrice: 'HAS_PRICE',
        description: 'Giày sneaker unisex phong cách thể thao, phù hợp mọi lứa tuổi',
        imagesMediaTypes: ['image/jpeg', 'image/png', 'image/webp'],
        tags: ['giày', 'sneaker', 'unisex', 'thể thao'],
        customFields: [
          {
            customFieldId: 94,
            value: {
              value: '42'
            }
          },
          {
            customFieldId: 95,
            value: {
              value: 'Da tổng hợp'
            }
          }
        ],
        shipmentConfig: {
          widthCm: 35,
          heightCm: 15,
          lengthCm: 40,
          weightGram: 800
        },
        classifications: [
          {
            type: 'Màu sắc',
            price: {
              listPrice: 800000,
              salePrice: 650000,
              currency: 'VND'
            },
            customFields: [
              {
                label: 'Màu chính',
                type: 'select',
                required: true,
                value: {
                  value: 'Trắng'
                }
              },
              {
                label: 'Màu phụ',
                type: 'select',
                required: false,
                value: {
                  value: 'Đen'
                }
              }
            ]
          },
          {
            type: 'Kích thước',
            price: {
              listPrice: 800000,
              salePrice: 650000,
              currency: 'VND'
            },
            customFields: [
              {
                label: 'Size giày',
                type: 'select',
                required: true,
                value: {
                  value: '42'
                }
              },
              {
                label: 'Chiều rộng',
                type: 'select',
                required: false,
                value: {
                  value: 'Standard'
                }
              }
            ]
          },
          {
            type: 'Chất liệu',
            price: {
              listPrice: 850000,
              salePrice: 700000,
              currency: 'VND'
            },
            customFields: [
              {
                label: 'Chất liệu upper',
                type: 'text',
                required: true,
                value: {
                  value: 'Da tổng hợp + Mesh'
                }
              },
              {
                label: 'Chất liệu đế',
                type: 'text',
                required: true,
                value: {
                  value: 'Cao su EVA'
                }
              }
            ]
          },
          {
            type: 'Tính năng',
            price: {
              listPrice: 800000,
              salePrice: 650000,
              currency: 'VND'
            },
            customFields: [
              {
                label: 'Chống nước',
                type: 'boolean',
                required: false,
                value: {
                  value: 'true'
                }
              },
              {
                label: 'Độ bám',
                type: 'text',
                required: false,
                value: {
                  value: 'Cao'
                }
              }
            ]
          }
        ]
      },
      {
        name: 'Túi xách nữ da thật',
        price: {
          listPrice: 1200000,
          salePrice: 950000,
          currency: 'VND'
        },
        typePrice: 'HAS_PRICE',
        description: 'Túi xách nữ làm từ da thật 100%, thiết kế sang trọng',
        imagesMediaTypes: ['image/jpeg'],
        tags: ['túi xách', 'nữ', 'da thật', 'sang trọng'],
        customFields: [
          {
            customFieldId: 96,
            value: {
              value: 'Medium'
            }
          }
        ],
        shipmentConfig: {
          widthCm: 40,
          heightCm: 10,
          lengthCm: 45,
          weightGram: 600
        },
        classifications: [
          {
            type: 'Màu sắc',
            price: {
              listPrice: 1200000,
              salePrice: 950000,
              currency: 'VND'
            },
            customFields: [
              {
                label: 'Màu da',
                type: 'select',
                required: true,
                value: {
                  value: 'Nâu cognac'
                }
              }
            ]
          },
          {
            type: 'Kích thước',
            price: {
              listPrice: 1200000,
              salePrice: 950000,
              currency: 'VND'
            },
            customFields: [
              {
                label: 'Kích thước',
                type: 'select',
                required: true,
                value: {
                  value: 'Medium (30x25x12cm)'
                }
              },
              {
                label: 'Dung tích',
                type: 'text',
                required: false,
                value: {
                  value: '8-10 lít'
                }
              }
            ]
          },
          {
            type: 'Phụ kiện',
            price: {
              listPrice: 1250000,
              salePrice: 1000000,
              currency: 'VND'
            },
            customFields: [
              {
                label: 'Dây đeo',
                type: 'select',
                required: true,
                value: {
                  value: 'Có thể tháo rời'
                }
              },
              {
                label: 'Khóa túi',
                type: 'text',
                required: false,
                value: {
                  value: 'Khóa nam châm cao cấp'
                }
              }
            ]
          }
        ]
      },
      {
        name: 'Đồng hồ thông minh',
        price: {
          listPrice: 2500000,
          salePrice: 2000000,
          currency: 'VND'
        },
        typePrice: 'HAS_PRICE',
        description: 'Đồng hồ thông minh với nhiều tính năng theo dõi sức khỏe',
        imagesMediaTypes: ['image/jpeg', 'image/png'],
        tags: ['đồng hồ', 'thông minh', 'smartwatch', 'sức khỏe'],
        customFields: [
          {
            customFieldId: 97,
            value: {
              value: '44mm'
            }
          },
          {
            customFieldId: 98,
            value: {
              value: 'iOS, Android'
            }
          }
        ],
        shipmentConfig: {
          widthCm: 15,
          heightCm: 8,
          lengthCm: 20,
          weightGram: 300
        },
        classifications: [
          {
            type: 'Màu sắc',
            price: {
              listPrice: 2500000,
              salePrice: 2000000,
              currency: 'VND'
            },
            customFields: [
              {
                label: 'Màu vỏ',
                type: 'select',
                required: true,
                value: {
                  value: 'Đen'
                }
              },
              {
                label: 'Màu dây',
                type: 'select',
                required: true,
                value: {
                  value: 'Đen'
                }
              }
            ]
          },
          {
            type: 'Kích thước',
            price: {
              listPrice: 2500000,
              salePrice: 2000000,
              currency: 'VND'
            },
            customFields: [
              {
                label: 'Kích thước mặt',
                type: 'select',
                required: true,
                value: {
                  value: '44mm'
                }
              },
              {
                label: 'Kích thước dây',
                type: 'select',
                required: true,
                value: {
                  value: 'M/L (140-210mm)'
                }
              }
            ]
          },
          {
            type: 'Tính năng',
            price: {
              listPrice: 2700000,
              salePrice: 2200000,
              currency: 'VND'
            },
            customFields: [
              {
                label: 'GPS',
                type: 'boolean',
                required: false,
                value: {
                  value: 'true'
                }
              },
              {
                label: 'Chống nước',
                type: 'text',
                required: false,
                value: {
                  value: '50m'
                }
              },
              {
                label: 'Thời lượng pin',
                type: 'text',
                required: false,
                value: {
                  value: '7 ngày'
                }
              }
            ]
          }
        ]
      }
    ]
  })
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => BusinessCreateProductDto)
  products: BusinessCreateProductDto[];
}


