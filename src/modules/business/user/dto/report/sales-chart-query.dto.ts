import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum, IsDateString, Validate } from 'class-validator';
import { DateRangeValidator } from './report-overview-query.dto';

/**
 * Enum cho cách nhóm dữ liệu biểu đồ
 */
export enum ChartGroupByEnum {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  QUARTER = 'quarter'
}

/**
 * DTO cho query parameters của API biểu đồ doanh thu
 */
export class SalesChartQueryDto {
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> bắt đầu (YYYY-MM-DD)',
    example: '2024-01-01',
    type: String,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Ngày bắt đầu phải có định dạng YYYY-MM-DD' })
  startDate?: string;

  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> kết thúc (YYYY-MM-DD)',
    example: '2024-12-31',
    type: String,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Ngày kết thúc phải có định dạng YYYY-MM-DD' })
  @Validate(DateRangeValidator)
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Cách nhóm dữ liệu theo thời gian',
    enum: ChartGroupByEnum,
    example: ChartGroupByEnum.MONTH,
    default: ChartGroupByEnum.MONTH,
  })
  @IsOptional()
  @IsEnum(ChartGroupByEnum, { message: 'Cách nhóm dữ liệu không hợp lệ' })
  groupBy?: ChartGroupByEnum = ChartGroupByEnum.MONTH;

  @ApiPropertyOptional({
    description: 'Đơn vị tiền tệ',
    example: 'VND',
    default: 'VND',
  })
  @IsOptional()
  @IsString({ message: 'Đơn vị tiền tệ phải là chuỗi' })
  currency?: string = 'VND';
}
