/**
 * Examples cho API tạo sản phẩm
 */
export const PRODUCT_CREATE_EXAMPLES = {
  physical: {
    summary: 'Sản phẩm vật lý',
    description: 'Ví dụ tạo sản phẩm vật lý (áo thun)',
    value: {
      name: 'Áo thun nam basic',
      productType: 'PHYSICAL',
      typePrice: 'HAS_PRICE',
      price: {
        listPrice: 200000,
        salePrice: 150000,
        currency: 'VND'
      },
      description: 'Áo thun nam chất liệu cotton cao cấp',
      imagesMediaTypes: ['image/jpeg', 'image/png'],
      tags: ['áo thun', 'nam', 'cotton'],
      customFields: [
        {
          customFieldId: 5,
          value: {
            value: 'XL'
          }
        }
      ],
      shipmentConfig: {
        widthCm: 25,
        heightCm: 5,
        lengthCm: 30,
        weightGram: 200
      },
      classifications: [
        {
          type: 'Màu sắc',
          price: {
            listPrice: 200000,
            salePrice: 150000,
            currency: 'VND'
          },
          customFields: [
            {
              customFieldId: 5,
              value: {
                value: 'Đỏ'
              }
            }
          ]
        }
      ],
      inventory: {
        warehouseId: 1,
        availableQuantity: 100,
        sku: 'SKU-001',
        barcode: '1234567890123'
      }
    }
  },
  digital: {
    summary: 'Sản phẩm số',
    description: 'Ví dụ tạo sản phẩm số (khóa học online)',
    value: {
      name: 'Khóa học lập trình React',
      productType: 'DIGITAL',
      typePrice: 'HAS_PRICE',
      price: {
        listPrice: 1500000,
        salePrice: 1200000,
        currency: 'VND'
      },
      description: 'Khóa học lập trình React từ cơ bản đến nâng cao',
      imagesMediaTypes: ['image/jpeg'],
      tags: ['khóa học', 'lập trình', 'react', 'online'],
      advancedInfo: {
        purchaseCount: 0,
        digitalFulfillmentFlow: {
          deliveryMethod: 'dashboard_download',
          deliveryTiming: 'immediate',
          deliveryDelayMinutes: 0,
          accessStatus: 'pending'
        },
        digitalOutput: {
          outputType: 'online_course',
          accessLink: 'https://course.example.com/activate?token=abc123',
          loginInfo: {
            username: 'auto_generated',
            password: 'temp_password'
          },
          usageInstructions: 'Vui lòng đăng nhập bằng thông tin được cung cấp để truy cập khóa học'
        }
      }
    }
  },
  event: {
    summary: 'Sự kiện',
    description: 'Ví dụ tạo sự kiện (hội thảo) - Giá được lấy từ ticket types',
    value: {
      name: 'Hội thảo Marketing Digital 2024',
      productType: 'EVENT',
      description: 'Hội thảo về xu hướng Marketing Digital năm 2024',
      imagesMediaTypes: ['image/jpeg'],
      tags: ['hội thảo', 'marketing', 'digital', '2024'],
      advancedInfo: {
        purchaseCount: 0,
        eventFormat: 'HYBRID',
        eventLink: 'https://zoom.us/j/123456789',
        eventLocation: 'Trung tâm Hội nghị Quốc gia, Hà Nội',
        startDate: 1704067200000,
        endDate: 1704153600000,
        timezone: 'Asia/Ho_Chi_Minh',
        ticketTypes: [
          {
            name: 'Vé thường',
            price: 400000,
            startTime: 1704067200000,
            endTime: 1704153600000,
            timezone: 'Asia/Ho_Chi_Minh',
            description: 'Vé tham gia hội thảo cơ bản',
            quantity: 100,
            minQuantityPerPurchase: 1,
            maxQuantityPerPurchase: 5,
            status: 'PENDING',
            imagesMediaTypes: ['image/jpeg']
          }
        ]
      }
    }
  },
  service: {
    summary: 'Dịch vụ',
    description: 'Ví dụ tạo dịch vụ (tư vấn)',
    value: {
      name: 'Dịch vụ tư vấn kinh doanh',
      productType: 'SERVICE',
      typePrice: 'HAS_PRICE',
      price: {
        listPrice: 2000000,
        salePrice: 1800000,
        currency: 'VND'
      },
      description: 'Dịch vụ tư vấn chiến lược kinh doanh cho doanh nghiệp',
      imagesMediaTypes: ['image/jpeg'],
      tags: ['tư vấn', 'kinh doanh', 'chiến lược'],
      advancedInfo: {
        purchaseCount: 0,
        servicePackages: [
          {
            name: 'Gói tư vấn cơ bản',
            price: 1800000,
            startTime: 1704067200000,
            endTime: 1704153600000,
            timezone: 'Asia/Ho_Chi_Minh',
            description: 'Gói tư vấn cơ bản bao gồm 3 buổi tư vấn online',
            quantity: 50,
            minQuantityPerPurchase: 1,
            maxQuantityPerPurchase: 3,
            status: 'PENDING',
            imagesMediaTypes: ['image/jpeg']
          }
        ]
      }
    }
  },
  combo: {
    summary: 'Combo sản phẩm',
    description: 'Ví dụ tạo combo sản phẩm',
    value: {
      name: 'Combo áo thun + quần jean',
      productType: 'COMBO',
      typePrice: 'HAS_PRICE',
      price: {
        listPrice: 800000,
        salePrice: 650000,
        currency: 'VND'
      },
      description: 'Combo áo thun nam + quần jean với giá ưu đãi',
      imagesMediaTypes: ['image/jpeg'],
      tags: ['combo', 'áo thun', 'quần jean', 'ưu đãi'],
      inventory: {
        warehouseId: 1,
        availableQuantity: 50,
        sku: 'COMBO-001',
        barcode: '1234567890124'
      }
    }
  }
};
