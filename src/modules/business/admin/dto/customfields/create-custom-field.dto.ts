import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsObject, IsString, Matches } from 'class-validator';

/**
 * DTO cho việc tạo trường tùy chỉnh mới
 */
export class CreateCustomFieldDto {

  @ApiProperty({
    description: '<PERSON> cấu hình (phải là unique)',
    example: 'product_color',
  })
  @IsNotEmpty()
  @IsString()
  configId: string;

  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: '<PERSON>àu sắc',
  })
  @IsNotEmpty()
  @IsString()
  label: string;

  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
  })
  @IsNotEmpty()
  @IsString()
  type: string;

  @ApiProperty({
    description: 'Trường bắt buộc hay không',
    example: true,
  })
  @IsNotEmpty()
  @IsBoolean()
  required: boolean;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> hình chi tiết',
    example: {
      placeholder: '<PERSON><PERSON><PERSON><PERSON> màu sắc',
      maxLength: 50,
      description: '<PERSON><PERSON><PERSON> sắ<PERSON> chính của sản phẩm',
    },
  })
  @IsNotEmpty()
  @IsObject()
  configJson: any;
}
