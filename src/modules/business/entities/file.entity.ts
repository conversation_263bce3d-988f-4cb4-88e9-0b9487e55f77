import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng files trong cơ sở dữ liệu
 * Bảng quản lý tệp tin trong hệ thống
 */
@Entity('files')
export class File {
  /**
   * ID của tệp tin
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID thư mục chứa tệp tin
   */
  @Column({
    name: 'folder_id',
    nullable: false,
    comment: 'ID thư mục chứa tệp tin',
  })
  folderId: number;

  /**
   * Tên tệp tin
   */
  @Column({
    name: 'name',
    length: 255,
    nullable: false,
    comment: 'Tên tệp tin',
  })
  name: string;

  /**
   * Khóa lưu trữ trên hệ thống storage
   */
  @Column({
    name: 'storage_key',
    length: 1024,
    nullable: false,
    comment: 'Đường dẫn hoặc key thực tế trên hệ thống storage',
  })
  storageKey: string;

  /**
   * <PERSON><PERSON>ch thước tệp tin (byte)
   */
  @Column({
    name: 'size',
    type: 'bigint',
    default: 0,
    comment: '<PERSON><PERSON><PERSON> thước tệp tin (byte)',
  })
  size: number;

  /**
   * Thời gian tạo (millis)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: false,
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    comment: 'Thời gian tạo (millis)',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (millis)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: false,
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    comment: 'Thời gian cập nhật (millis)',
  })
  updatedAt: number;
}