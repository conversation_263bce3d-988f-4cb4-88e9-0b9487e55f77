import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, Join<PERSON>olumn, Index } from 'typeorm';
import { User } from '@modules/user/entities';

/**
 * Entity cho địa chỉ của người dùng
 */
@Entity('user_addresses')
@Index(['userId', 'isDefault'])
export class UserAddress {
  /**
   * ID địa chỉ
   */
  @PrimaryGeneratedColumn({ type: 'bigint', comment: 'ID địa chỉ' })
  id: number;

  /**
   * ID người dùng sở hữu địa chỉ
   */
  @Column({ 
    name: 'user_id', 
    type: 'integer', 
    comment: 'ID người dùng sở hữu địa chỉ' 
  })
  userId: number;

  /**
   * Tên người nhận
   */
  @Column({ 
    name: 'recipient_name', 
    type: 'varchar', 
    length: 255, 
    comment: 'Tên người nhận' 
  })
  recipientName: string;

  /**
   * <PERSON><PERSON> điện thoại người nhận
   */
  @Column({ 
    name: 'recipient_phone', 
    type: 'varchar', 
    length: 20, 
    comment: 'Số điện thoại người nhận' 
  })
  recipientPhone: string;

  /**
   * Địa chỉ chi tiết
   */
  @Column({ 
    name: 'address', 
    type: 'varchar', 
    length: 500, 
    comment: 'Địa chỉ chi tiết' 
  })
  address: string;

  /**
   * Tỉnh/Thành phố
   */
  @Column({ 
    name: 'province', 
    type: 'varchar', 
    length: 100, 
    nullable: true,
    comment: 'Tỉnh/Thành phố' 
  })
  province?: string;

  /**
   * Quận/Huyện
   */
  @Column({ 
    name: 'district', 
    type: 'varchar', 
    length: 100, 
    nullable: true,
    comment: 'Quận/Huyện' 
  })
  district?: string;

  /**
   * Phường/Xã
   */
  @Column({ 
    name: 'ward', 
    type: 'varchar', 
    length: 100, 
    nullable: true,
    comment: 'Phường/Xã' 
  })
  ward?: string;

  /**
   * Mã bưu điện
   */
  @Column({ 
    name: 'postal_code', 
    type: 'varchar', 
    length: 10, 
    nullable: true,
    comment: 'Mã bưu điện' 
  })
  postalCode?: string;

  /**
   * Có phải địa chỉ mặc định không
   */
  @Column({ 
    name: 'is_default', 
    type: 'boolean', 
    default: false,
    comment: 'Có phải địa chỉ mặc định không' 
  })
  isDefault: boolean;

  /**
   * Loại địa chỉ (home, office, other)
   */
  @Column({ 
    name: 'address_type', 
    type: 'varchar', 
    length: 20, 
    default: 'home',
    comment: 'Loại địa chỉ (home, office, other)' 
  })
  addressType: string;

  /**
   * Ghi chú địa chỉ
   */
  @Column({ 
    name: 'note', 
    type: 'text', 
    nullable: true,
    comment: 'Ghi chú địa chỉ' 
  })
  note?: string;

  /**
   * Trạng thái hoạt động
   */
  @Column({ 
    name: 'is_active', 
    type: 'boolean', 
    default: true,
    comment: 'Trạng thái hoạt động' 
  })
  isActive: boolean;

  /**
   * Thời gian tạo (timestamp)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    comment: 'Thời gian tạo địa chỉ',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (timestamp)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    comment: 'Thời gian cập nhật địa chỉ',
  })
  updatedAt: number;

  /**
   * Relation với User
   */
  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;
}
