import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

/**
 * Client để tương tác với Zalo API
 */
@Injectable()
export class ZaloApiClient {
  private readonly logger = new Logger(ZaloApiClient.name);
  private readonly apiUrl: string;
  private readonly client: AxiosInstance;

  constructor(private readonly configService: ConfigService) {
    this.apiUrl = 'https://openapi.zalo.me/v2.0';
    this.client = axios.create({
      baseURL: this.apiUrl,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Thêm interceptor để log request và response
    this.setupInterceptors();
  }

  /**
   * Thiết lập interceptors cho axios
   */
  private setupInterceptors(): void {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        this.logger.debug(`Sending request to ${config.url}`);
        return config;
      },
      (error) => {
        this.logger.error(`Request error: ${error.message}`);
        return Promise.reject(error);
      },
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        this.logger.debug(`Received response from ${response.config.url}`);
        return response;
      },
      (error) => {
        if (error.response) {
          this.logger.error(
            `Response error: ${error.response.status} - ${JSON.stringify(error.response.data)}`,
          );
        } else if (error.request) {
          this.logger.error(`No response received: ${error.message}`);
        } else {
          this.logger.error(`Request setup error: ${error.message}`);
        }
        return Promise.reject(error);
      },
    );
  }

  /**
   * Gửi request GET đến Zalo API
   * @param endpoint Endpoint của API
   * @param accessToken Access token của Official Account
   * @param params Tham số query
   * @returns Kết quả từ API
   */
  async get<T = any>(endpoint: string, accessToken: string, params?: any): Promise<T> {
    const config: AxiosRequestConfig = {
      headers: {
        access_token: accessToken,
      },
      params,
    };

    try {
      const response: AxiosResponse<T> = await this.client.get(endpoint, config);
      return response.data;
    } catch (error) {
      this.logger.error(`GET request to ${endpoint} failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Gửi request POST đến Zalo API
   * @param endpoint Endpoint của API
   * @param accessToken Access token của Official Account
   * @param data Dữ liệu gửi đi
   * @returns Kết quả từ API
   */
  async post<T = any>(endpoint: string, accessToken: string, data?: any): Promise<T> {
    const config: AxiosRequestConfig = {
      headers: {
        access_token: accessToken,
      },
    };

    try {
      const response: AxiosResponse<T> = await this.client.post(endpoint, data, config);
      return response.data;
    } catch (error) {
      this.logger.error(`POST request to ${endpoint} failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Gửi request PUT đến Zalo API
   * @param endpoint Endpoint của API
   * @param accessToken Access token của Official Account
   * @param data Dữ liệu gửi đi
   * @returns Kết quả từ API
   */
  async put<T = any>(endpoint: string, accessToken: string, data?: any): Promise<T> {
    const config: AxiosRequestConfig = {
      headers: {
        access_token: accessToken,
      },
    };

    try {
      const response: AxiosResponse<T> = await this.client.put(endpoint, data, config);
      return response.data;
    } catch (error) {
      this.logger.error(`PUT request to ${endpoint} failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Gửi request DELETE đến Zalo API
   * @param endpoint Endpoint của API
   * @param accessToken Access token của Official Account
   * @param params Tham số query
   * @returns Kết quả từ API
   */
  async delete<T = any>(endpoint: string, accessToken: string, params?: any): Promise<T> {
    const config: AxiosRequestConfig = {
      headers: {
        access_token: accessToken,
      },
      params,
    };

    try {
      const response: AxiosResponse<T> = await this.client.delete(endpoint, config);
      return response.data;
    } catch (error) {
      this.logger.error(`DELETE request to ${endpoint} failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Làm mới access token
   * @param refreshToken Refresh token của Official Account
   * @param appId ID của ứng dụng Zalo
   * @param appSecret Secret của ứng dụng Zalo
   * @returns Kết quả từ API
   */
  async refreshAccessToken(refreshToken: string, appId: string, appSecret: string): Promise<any> {
    const endpoint = '/oauth/access_token';
    const data = {
      refresh_token: refreshToken,
      app_id: appId,
      grant_type: 'refresh_token',
      app_secret: appSecret,
    };

    try {
      const response: AxiosResponse = await this.client.post(endpoint, data);
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to refresh access token: ${error.message}`);
      throw error;
    }
  }
}
